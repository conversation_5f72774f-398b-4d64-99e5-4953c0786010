@extends('layout.innerlayout')
@section('content')
    <!-- App Capsule -->
    <div id="appCapsule">


        <!-- TODO: Show barcode in the list -->

        <!-- <script src="https://unpkg.com/html5-qrcode@2.0.9/dist/html5-qrcode.min.js"></script> -->
        <!-- <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script> -->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

        <style>
            .result {
                background-color: green;

            }

            .row {
                display: flex;
            }
        </style>



    <div id="loading" class="toast-box toast-center" style="z-index: 9999">
        <div class="in">
            <div class="spinner-border text-success" role="status">
            </div>
            <div style="margin-top: 5px" class="text">
                Loading.....
            </div>
        </div>
    </div>

@if (session()->has('message'))
<div id="toast-3" class="toast-box toast-center show">
    <div class="in">

        <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
        <div class="text">
            {{ session()->get('message') }}
        </div>
    </div>
    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
</div>
@endif
@if (session()->has('errors'))
<div id="toast-3" class="toast-box toast-center show">
    @foreach ($errors->all() as $error)
        <div class="in">

            <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
            <div class="text">
                {{ $error }}
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
    @endforeach
</div>
@endif
@if (Session::has('error'))
<div id="toast-3" class="toast-box toast-center show">
    <div class="in">

        <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
        <div class="text">
            {{ Session::get('error') }}
        </div>
    </div>
    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
</div>
@endif


        <!-- Modal Basic -->
        <div class="modal fade modalbox" id="ModalBasic" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scan Product</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col">
                                <div style="width:100%" id="reader"></div>
                            </div>

                        </div>
                        <br>
                        <div style="text-align: center">OR Enter Manually</div>
                        <br>

                        <div class="invoice">
                            <div class="invoice-page-header">
                                <div class="invoice-logo">
                                    <div class="">
                                        <input type="text" name="barcode_input" id="barcode_input" value="" />
                                    </div>
                                </div>
                                <div class="invoice-id">
                                    <a href="javascript:void(0);" onclick="submitBarcode()"
                                        class="btn btn-outline-secondary btn-sm">Submit</a>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>




        <!-- Modal Form -->
        <div class="modal fade modalbox" id="ModalQuantity" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enter Pick Quantity</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="section mt-4 mb-5">
                                <div class="form-group basic">

                                    @if (isset($itemWisePickingSetting) && $itemWisePickingSetting == 1)
                                    <div class="input-wrapper" style="margin-bottom: 20px">
                                    <input type="text" name="barcode_scan" placeholder="Scan Product Barcode" id="barcode_scan_itemwise_pick"  style="width: 100%;" value="" />
                                    </div>
                                    @endif
                                    <div class="input-wrapper">
                                        <label class="form-label" for="quantity">Expected <span id="picking_item_qty_input"> </span></label>
                                        <input type="number" min="1" max="" class="form-control" name="quantity" onkeyup=enforceMinMax(this) id="quantity" required placeholder="Enter Quantity">
                                        <i class="clear-input">
                                            <ion-icon name="close-circle"></ion-icon>
                                        </i>
                                    </div>
                                </div>


                                    <div class="mt-2">
                                        <button type="button" onclick="quantityPicked()"  data-bs-dismiss="modal" class="btn btn-primary btn-block btn-lg">Submit</button>
                                    </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Form -->

        <!-- Modal Form -->
        <div class="modal fade modalbox" id="ModalConfirmed" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Item Tote Location</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="section mt-4 mb-5">

                            <div class="section full mt-3">
                                <div class="invoice" id="picking_item_tote_location" style="text-align:center; font-size:x-large;">

                                        <span><b>SKU : </b></span>
                                        <br>
                                </div>
                            </div>

                            <form action="/item-picked" id="item-picked-submit-form" method="POST">

                                @csrf

                                <input type="hidden" class="form-control" id="pick_item" name="pick_item">

                                <div class="mt-2">
                                    <button type="submit" class="btn btn-primary btn-block btn-lg" id="btnPicked"  onclick="javascript:$('#btnPicked').hide();$('#picked-loader').show();">Picked</button>
                                    <div style="text-align: center;"> <div class="spinner-border text-primary" id="picked-loader" role="status" style="display:none"></div> </div>
                                </div>

                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Form -->




        <div class="section full mt-1 mb-2">

            @if ($data['all_picked'])

                <div class="section full mt-3">

                    <div class="invoice" style="text-align:center; font-size:x-large;">

                        <span><b>Trolley ID : </b></span>
                        <br>

                        <div class="mt-2">
                            <form style="margin:0px;" action="/assign-picklist/submit-to-pd" method="POST">
                                <input type="text" name="trolley_number" placeholder="Scan Trolley" style="width:75%" />
                                {{ csrf_field() }}
                                <br>
                                <br>
                                <button type="submit"  class="btn btn-primary" >Submit to Packing Desk</button>
                            </form>
                        </div>
                    </div>
                </div>

            @else

                <div class="section full mt-3">
                    
                    <div class="invoice" id="picking_item" style="font-size:x-large;">
                   
                            <span id="picking_item_product_name"><b>Product : </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_barcode"><b>Barcode : </b><br></span>
                           
                            <div> &nbsp;</div>
                            <span id="picking_item_sku"><b>SKU : </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_qty">Quantity : </span>
                            <div> &nbsp;</div>
                            <span id="picking_item_inventory_path"></span>
                           
                        <div class="mt-3">
                            <input type="text" name="barcode_input_new" placeholder="Scan Product Barcode" id="barcode_input_new" autofocus style="width: 100%;" value="" />
                        </div>
                        <span style="float:right;padding-top:16px">
                              <button type="button" id="item_not_available_btn" onclick="inventoryNotAvailable()" class="btn btn-sm btn-outline-danger me-1 mb-1"  >Item Not Available</button>
                            </span> 
                    </div>
                </div>

                <!-- <div class="card cart-item mb-2">
                    <div class="card-body" style="text-align: center">
                        <a id="next_button" href="/pick" class="btn btn-outline-primary btn-sm" style="width:auto;height:50px">Suggest New Location</a>
                    </div>
                </div> -->

            @endif





            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#accordion003" style="color:#fff;background-color:#a8a0d0;">
                        <ion-icon name="list-outline"></ion-icon>&nbsp;Items List
                    </button>
                </h2>
                <div id="accordion003" class="accordion-collapse collapse">
                    <div class="accordion-body">

                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">SKU</th>
                                        <th scope="col">Location</th>
                                        <th scope="col">QTY</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <script>
                                        var my_arr = [];
                                    </script>

                                    @foreach ($order_items as $key => $item)
                                        <script>
                                            my_arr.push({!! json_encode($item) !!});
                                        </script>

                                        <tr @if (isset($item->picked) && $item->picked == 1) style="background-color:#00FF00;" @endif >
                                            <th scope="row">{{ ( (int) $key) + 1 }}</th>
                                            <td> {{ $item->sku }} <br> ( {{ $item->reference_id }} )</td>
                                            <td>{{ $item->inventory_path }}</td>
                                            <td><span>@if (isset($item->picked) && $item->picked == 1) {{ $item->quantity }} @else 0 @endif /{{ $item->quantity }}</span></td>
                                        </tr>
                                    @endforeach

                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>

        </div>




    </div>
    <!-- * App Capsule -->

    <div id="jsSuccess" class="toast-box toast-center">
            <div class="in">

                <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                <div class="text">
                    <span id="js_success_message"></span>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button" onclick="location.href='/pick'">CLOSE</button>
        </div>

        <div class="modal fade dialogbox" id="DialogIconedButtonInline" data-bs-backdrop="static" tabindex="-1"
            role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"> Are you sure ?</h5>
                    </div>
                  
                    <div class="modal-footer">
                        <div class="btn-inline">
                            <a href="#" class="btn btn-text-danger" data-bs-dismiss="modal">
                                <ion-icon name="close"></ion-icon>
                                NO
                            </a>
                            <a href="#" id="are_you_sure_yes_id" class="btn btn-text-primary">
                                <ion-icon name="checkmark-sharp"></ion-icon>
                                YES
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>


    <div class="toast-box toast-center" id="error_message_toast" style="z-index: 9999">
        <div class="in">
            <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
            <div class="text" id="error_text">
            </div>
        </div>
    </div>


    <div id="success_message_toast" class="toast-box toast-top">
        <div class="in">

            <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
            <div class="text" id="success_text">
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-error close-button">CLOSE</button>
    </div>

    <script type="text/javascript">
        var session = "{{ session('token') }}";
        var totatl_qty_scan = 0;

        var current_pick_items = {!! json_encode($current_pick_items) !!};
        var current_item = my_arr[0];
        var current_item_index = 0;
        var total_items = my_arr.length;
        var total_items_scanned = 0;
    

        console.log(current_pick_items['items'][0]['fulfillment_order_id']);

        $("#picking_item_product_name").html('Product : <b>'+current_pick_items['product_name']+'</b>');
        $("#picking_item_barcode").html('Barcode : <b>'+current_pick_items['barcode']+'</b>');
        $("#picking_item_sku").html('SKU : <b>'+current_pick_items['sku']+'</b>');
        $("#picking_item_qty").html('Quantity : <b>'+current_pick_items['quantity']+'</b>');
        $("#picking_item_qty_input").html('Quantity : <b>'+current_pick_items['quantity']+'</b>');
        $("#picking_item_inventory_path").html('Location : <b>'+current_pick_items['inventory_path']+'</b>');

        function quantityPicked() {

            var quantity = $("#quantity").val();
            console.log(quantity);

            var temp_items = [];
            var html = '';

            current_pick_items['items'].forEach(item => {

                if (quantity) {

                    if (item['quantity'] <= quantity) {

                        temp_items.push(item);
                        quantity -= item['quantity'];
                        html += '<span>Tote #'+item['tote_no']+' : <b>Quantity '+item['quantity']+'</b></span><br>';


                    } else if(item['quantity'] > quantity) {

                        var temp = item;
                        temp['quantity'] = quantity;
                        temp_items.push(temp);
                        quantity = 0;
                        html += '<span>Tote #'+item['tote_no']+' : <b>Quantity '+temp['quantity']+'</b></span><br>';

                    }

                }

            });

            $("#picking_item_tote_location").html(html);
            console.log(temp_items);

            $("#pick_item").val( JSON.stringify( temp_items ));

            @if($data['picking_mode'])
                $('#loading').show();
                $('#item-picked-submit-form').submit();

            @else
                $('#ModalConfirmed').modal('show');
            @endif

        }

        function onScanSuccess(qrCodeMessage) {
            console.log(qrCodeMessage);
            // html5QrcodeScanner.clear();
            $('#ModalBasic').modal('hide');

            if (qrCodeMessage == current_pick_items['barcode']) {

                $('#ModalQuantity').modal('show');
                $("#quantity").attr({ "max" : current_pick_items['quantity'] });

                @if (isset($itemWisePickingSetting) && $itemWisePickingSetting == 1)
                    setTimeout(function() {  $('#barcode_scan_itemwise_pick').focus()}, 1000);
                    $('#quantity').val(1);
                    $('#quantity').attr('readonly','readonly');

                    if (current_pick_items['quantity'] == 1) {
                        quantityPicked();
                    }

                @else
                    setTimeout(function() {  $('#quantity').focus()}, 1000);
                @endif


            } else {
                showError("Barcode not matched, please scan again");
            }

        }

        function onScanError(errorMessage) {
            //handle scan error
        }
        // var html5QrcodeScanner = new Html5QrcodeScanner(
        //     "reader", {
        //         fps: 10,
        //         qrbox: 250
        //     });
        // html5QrcodeScanner.render(onScanSuccess, onScanError);

        function showModal() {
            $('#barcode_input').val("");
            $("#error_message_toast").removeClass("show");

            $('#ModalBasic').modal('show');
            // html5QrcodeScanner = new Html5QrcodeScanner(
            //     "reader", {
            //         fps: 10,
            //         qrbox: 250
            //     });
            // html5QrcodeScanner.render(onScanSuccess, onScanError);

        }

        function submitBarcode() {
            // temp($('#barcode_input').val());
            onScanSuccess($('#barcode_input').val());
        }

        function showError(error) {
            $("#error_text").text(error);
            toastbox('error_message_toast', 2000);
        }

        function showMessage(message) {
            $("#success_text").text(message);
            $("#success_message_toast").addClass("show");
        }

        $('#barcode_input_new').keypress(function(event) {
            if (event.keyCode == 13) {
                onScanSuccess($('#barcode_input_new').val());
                $('#barcode_input_new').val("");
            }
        });
        @if (isset($itemWisePickingSetting) && $itemWisePickingSetting == 1)
        $('#barcode_scan_itemwise_pick').keypress(function(event) {
            if (event.keyCode == 13) {
                $('#quantity').attr('readonly','readonly');
                var item_barcode = $(this).val();
                if(item_barcode == current_pick_items['barcode']){
                    if($('#quantity').val() < current_pick_items['quantity'] )
                    {
                        $('#quantity').val( +$('#quantity').val() + 1 );

                        if($('#quantity').val() == current_pick_items['quantity']) {
                            quantityPicked();
                        }

                    } else if($('#quantity').val() == current_pick_items['quantity']) {
                        quantityPicked();
                    }
                    else{
                        showError("QTY cannot be more than " + current_pick_items['quantity']);
                    }
                }
                else{
                    showError("Wrong Product");
                }
                $('#barcode_scan_itemwise_pick').val("");
            }


        });
        @endif

        function enforceMinMax(el) {
            if (el.value != "") {
                if (parseInt(el.value) < parseInt(el.min)) {
                    el.value = el.min;
                    showError("Value cannot be less than " + el.min);
                }
                if (parseInt(el.value) > parseInt(el.max)) {
                    el.value = el.max;
                    showError("Value cannot be more than " + el.max);
                }
            }

        }
        let sec = 5;
        let countdown = null;
        const updateButton = () => {
            $("#are_you_sure_yes_id").html(`wait ${sec}s`);  
            $("#are_you_sure_yes_id").removeAttr("onclick");
            if (sec === 0) {
                clearInterval(countdown);
                sec = 5;
                $("#are_you_sure_yes_id").html(`<ion-icon name="checkmark-sharp"></ion-icon> Yes`);  
                $("#are_you_sure_yes_id").attr('onclick', 'inventoryNotAvailableOnYes();');

                return;
            }

            sec--;
        }

        function inventoryNotAvailable() {
            $(document).ready(function(){
                $('#DialogIconedButtonInline').modal('show'); 
                

                updateButton();
                countdown = setInterval(function() {
                    updateButton();
                }, 1000);
                return;
            });

        }

        function inventoryNotAvailableOnYes() {
            $(document).ready(function(){
            $('#DialogIconedButtonInline').modal('hide'); 
            $('#item_not_available_btn').attr("disabled", true);
            $('#loading').show();
            var Data = new Object();
            Data.fo_id = current_pick_items['items'][0]['fulfillment_order_id'];
            Data.fo_item_id = current_pick_items['items'][0]['item_id'];
            Data.ffc_location_id = current_pick_items['items'][0]['ffc_location_id'];
            Data.quantity = current_pick_items['quantity'];

            
            $.ajax({
                    url: '{{env("UNITY_URL")}}/api/ffc/remove-committed-fo-unpicked-item',
                    data: Data,
                    crossDomain: true,
                dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session
                    },
                    success: function(response) {
                        $('#loading').hide();
                        console.log(response);
                        if(response.success == true){
                            $('#js_success_message').html("System is reassessing new location availability , please close the pop message to continue picking ");
                            $('#jsSuccess').show();
                        }else if(response.success == false){
                            showError(response.message);
                        }else{
                            showError("Something went wrong");
                        }
                        $('#item_not_available_btn').attr("disabled", true);
                    },

                });
            });

        }


        function pageUnload()
        {
            //return 'Are you sure you want to leave?';
        }

        $(window).bind('beforeunload', pageUnload);
    </script>
    </div>
    <!-- * App Capsule -->
@endsection
<b></b>
