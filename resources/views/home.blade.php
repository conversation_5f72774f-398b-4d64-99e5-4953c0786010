@extends('layout.mainlayout')
@section('content')
    <!-- App Capsule -->
    <div id="appCapsule">

        @if (session()->has('message'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                        {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        <div class="section mt-3 mb-3">

            <ul class="listview image-listview media mb-2">
                <li>
                    <a href="/security" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <span class="iconedbox iconedbox-xl">
                            <ion-icon class="bg-primary" name="enter-sharp"></ion-icon>
                        </span>
                        <h2>RECEIVING</h2>
                    </a>
                </li>
                <!-- <li>
                    <a href="/putaway" class="item">
                        <div class="imageWrapper">
                            <img src="assets/img/sample/photo/2.jpg" alt="image" class="imaged w64">
                        </div>
                        <div class="in">
                            <div>
                               Putaway
                            </div>
                        </div>
                    </a>
                </li> -->
                <li>

                        <a data-bs-toggle="offcanvas" class="item" href="#offcanvas-bottom">
                        <span class="iconedbox iconedbox-xl">
                            <ion-icon class="bg-primary" name="push-sharp"></ion-icon>
                        </span>
                        <h2>PUT-AWAY</h2>
                    </a>
                </li>
                <li>
                    <a href="/pick" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <span class="iconedbox iconedbox-xl">
                            <ion-icon class="bg-primary" name="reader-sharp"></ion-icon>
                        </span>
                        <h2>PICK</h2>
                    </a>
                </li>
                <li>
                    <a href="/transfer-outbound" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <span class="iconedbox iconedbox-xl">
                            <ion-icon class="bg-primary" name="exit-sharp"></ion-icon>
                        </span>
                        <h2>TRANSFER OUTBOUND</h2>
                    </a>
                </li>
                <li>
                    <a href="/stock-picking" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                            <span class="iconedbox iconedbox-xl">
                                <ion-icon class="bg-primary" name="repeat-sharp"></ion-icon>
                            </span>
                            <h2>STOCK MOVEMENT</h2>
                        </a>
                </li>
            </ul>
        </div>
        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>

    </div>
    <div class="offcanvas offcanvas-bottom" tabindex="-1" id="offcanvas-bottom">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">Which Method?</h5>
            <a href="#" class="offcanvas-close" data-bs-dismiss="offcanvas">
                <ion-icon name="close-outline"></ion-icon>
            </a>
        </div>
        <div class="offcanvas-body">
            <div>
                    <a href="/putaway_location_first" class="btn btn-primary btn-lg btn-block" onclick="javascript: freeze_click = true; $('#loading').show();">Location First</a>
                    <p>&nbsp;</p>
                    <a href="/pending_putaway" class="btn btn-primary btn-lg btn-block" onclick="javascript: freeze_click = true; $('#loading').show();">Product First</a>
            </div>
        </div>
    </div>
    <!-- * App Capsule -->
@endsection
<b></b>



<script>

    let freeze_click = false; // just modify that variable to disable all clics events

    document.addEventListener("click", e => {
        if (freeze_click) {
            e.stopPropagation();
            e.preventDefault();
        }
    }, true);

</script>
