@extends('layout.innerlayout')
@section('content')
    <!-- App Capsule -->
    <div id="appCapsule">

        <!-- <script src="https://unpkg.com/html5-qrcode@2.0.9/dist/html5-qrcode.min.js"></script> -->
        <!-- <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script> -->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

        <style>
            .result {
                background-color: green;

            }

            .row {
                display: flex;
            }
        </style>


        <!-- Modal Basic -->
        <div class="modal fade modalbox" id="ModalBasic" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scan Product</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col">
                                <div style="width:100%" id="reader"></div>
                            </div>

                        </div>
                        <br>
                        <div style="text-align: center">OR Enter Manually</div>
                        <br>

                        <div class="invoice">
                            <div class="invoice-page-header">
                                <div class="invoice-logo">
                                    <div class="">
                                        <input type="text" name="barcode_input" id="barcode_input" value="" />
                                    </div>
                                </div>
                                <div class="invoice-id">
                                    <a href="javascript:void(0);" onclick="submitBarcode()"
                                        class="btn btn-outline-secondary btn-sm">Submit</a>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>




        {{-- <div class="modal fade modalbox" id="ModalQuantity2" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enter Pick Quantity</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">

                        <input type="text" name="quantity_input" id="quantity_input" value="" />
                        <br>
                        <a href="javascript:void(0);" onclick="submitPick()" class="btn btn-outline-secondary btn-sm">Submit</a>

                    </div>
                </div>
            </div>
        </div> --}}
        <!-- * Modal Basic -->

        <!-- Modal Form -->
        <div class="modal fade modalbox" id="ModalQuantity" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enter Pick Quantity</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="section mt-4 mb-5">
                            <form action="/transfer-outbound/item-picked" id="to-picked-form" onsubmit="$('#picked_quantity_button').attr('disabled', true);" method="POST">
                                <div class="form-group basic">

                                    @if (isset($item_wise_transfer_order_setting) && $item_wise_transfer_order_setting)
                                        <div class="input-wrapper" style="margin-bottom: 20px">
                                            <input type="text" name="barcode_scan" placeholder="Scan Product Barcode" id="barcode_scan_itemwise_pick"  style="width: 100%;" value="" />
                                        </div>
                                    @endif


                                    <div class="input-wrapper">
                                        <label class="form-label" for="quantity">Quantity</label>
                                        <input type="number" min="1" max="" class="form-control" name="quantity" id="quantity" required placeholder="enter quantity">
                                        <i class="clear-input">
                                            <ion-icon name="close-circle"></ion-icon>
                                        </i>
                                    </div>
                                </div>

                                @csrf

                                <input type="hidden" class="form-control" id="pick_item" name="pick_item" >
                                <input type="hidden" class="form-control" value="{{$order_data->stock_transfer_fulfillment_order_id}}" name="o_id">
                                <input type="hidden" class="form-control" value="{{$order_data->seller_location_id}}" name="seller_location_id">
                                <input type="hidden" class="form-control" value="{{$automated_selection}}" name="automated_selection">


                                <div class="mt-2">
                                    <button type="button" id="picked_quantity_button" onclick="$('#to-picked-form').submit();"  class="btn btn-primary btn-block btn-lg">Submit</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Form -->




        <div class="section full mt-1 mb-2">

            @if($data['picked'])

                <form id="form_trolley" action="/transfer-outbound/submit-to-pd" method="POST">
                    <div class="section full me-1 mb-1" align="center">
                        {{ csrf_field() }}
                        Trolley Number :
                        <input type="text" name="trolley_number" size="10" />
                        <input type="hidden" name="tso_id"  value="{{$order_data->stock_transfer_fulfillment_order_id}}" />
                    </div>
                    <div class="col-md-12 bg-light text-center">
                        <button type="submit" class="btn btn-success btn-sm me-1 mb-1">Submit to Packing Desk</button>
                    </div>
                </form>

            @elseif($order_data->picklist_created)

                <div class="section full mt-3">
                    <div class="invoice" id="picking_item" style="font-size:x-large;">


                            <span id="picking_item_product_name"><b>Product : </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_barcode"><b>Barcode : </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_sku"><b>SKU : </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_qty">Quantity : </span>
                            <div> &nbsp;</div>
                            <span id="picking_item_inventory_path"></span>

                        <div class="mt-2">
                            <input type="text" name="barcode_input_new" placeholder="Scan Product Barcode" id="barcode_input_new"
                                autofocus style="width: 100%;" value="" />
                        </div>
                    </div>
                </div>



                {{-- <div class="card cart-item mb-2">
                    <div class="card-body" style="text-align: center">
                        <a id="next_button" href="/transfer-outbound/pickitems/{{ $order_data->stock_transfer_fulfillment_order_id }}" class="btn btn-outline-primary btn-sm" style="width:auto;height:50px">Suggest New Location</a>
                    </div>
                </div> --}}
            @endif

            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#accordion003">
                        <ion-icon name="list-outline"></ion-icon>&nbsp;Transfer Stock Order Items List
                    </button>
                </h2>
                <div id="accordion003" class="accordion-collapse @if($order_data->picklist_created) collapse @endif">
                    <div class="accordion-body">
                        
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">SKU</th>
                                        <th scope="col">Location</th>
                                        <th scope="col">QTY</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <script>
                                        var my_arr = [];
                                    </script>
        
                                    @foreach ($order_items as $key => $oi)
                                        <script>
                                            my_arr.push({!! json_encode($oi) !!});
                                        </script>
        
                                        <tr @if (isset($oi->picked) && $oi->picked == 1) style="background-color:#00FF00;" @endif >
                                            <th scope="row">{{ $key + 1 }}</th>
                                            <td> {{ $oi->sku }} </td>
                                            @if ($oi->inventory_path)
                                                <td>{{ $oi->inventory_path }}</td>
                                            @else
                                                <td><a href="assign-location/{{ $oi->item_id }}" class="btn btn-sm btn-success">Assign</a></td>
                                            @endif
                                            <td><span>@if (isset($oi->picked) && $oi->picked == 1) {{ $oi->quantity }} @else 0 @endif /{{ $oi->quantity }}</span></td>
                                        </tr>
                                    @endforeach
        
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>

        </div>




    </div>
    <!-- * App Capsule -->
   
    @if(session()->has('message'))
    <div id="toast-3" class="toast-box toast-top show">
        <div class="in">
            
            <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
            <div class="text">
            {{ session()->get('message') }}
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-error close-button">CLOSE</button>
    </div>

    @endif
    @if(session()->has('errors'))
    {{ $temp=""}}
    @foreach($errors->all() as $error)
    
    <div id="toast-3" class="toast-box toast-top show">
        <div class="in">
            
            <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
            <div class="text">
            {{ $temp = $temp.$error."." }}
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

     </div>
        @endforeach

    @endif
    @if(Session::has('error'))
    <div id="toast-3" class="toast-box toast-top show">
        <div class="in">
            
            <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
            <div class="text">
            {{ Session::get('error') }}
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
    </div>

    @endif
<!-- * toast top iconed -->

    <script type="text/javascript">
        var session = "{{ session('token') }}";
        var totatl_qty_scan = 0;

        var current_item = my_arr[0];
        var current_item_index = 0;
        var total_items = my_arr.length;
        var total_items_scanned = 0;

        $("#picking_item_sku").text('SKU : '+my_arr[current_item_index]['sku']);
        $("#picking_item_barcode").text('Barcode : '+my_arr[current_item_index]['barcode']);
        $("#picking_item_product_name").text('Product : '+my_arr[current_item_index]['product_name']);
        $("#picking_item_qty").text('Quantity : '+my_arr[current_item_index]['quantity']);
        $("#picking_item_inventory_path").text(my_arr[current_item_index]['inventory_path']);  

        function onScanSuccess(qrCodeMessage) {
            console.log(qrCodeMessage);
            // html5QrcodeScanner.clear();
            $('#ModalBasic').modal('hide');

            if (qrCodeMessage == my_arr[current_item_index]['barcode']) {

                $('#ModalQuantity').modal('show');
                $("#pick_item").val( JSON.stringify( my_arr[current_item_index]));
                $("#quantity").attr({ "max" : my_arr[current_item_index]['quantity'] });
                $("#quantity").attr({ "min" : my_arr[current_item_index]['quantity'] });

                @if (isset($item_wise_transfer_order_setting) && $item_wise_transfer_order_setting)
                    setTimeout(function() {  $('#barcode_scan_itemwise_pick').focus()}, 1000);
                    $('#quantity').val(1);
                    $('#quantity').attr('readonly','readonly');

                    if (my_arr[current_item_index]['quantity'] == 1) {
                        $('#to-picked-form').submit();
                    }

                @else
                    setTimeout(function() {  $('#quantity').focus()}, 1000);
                @endif


            } else {
                showError("Barcode not matched, please scan again");
            }

        }

        function onScanError(errorMessage) {
            //handle scan error
        }
        // var html5QrcodeScanner = new Html5QrcodeScanner(
        //     "reader", {
        //         fps: 10,
        //         qrbox: 250
        //     });
        // html5QrcodeScanner.render(onScanSuccess, onScanError);

        function showModal() {
            $('#barcode_input').val("");
            $("#error_message_toast").removeClass("show");

            $('#ModalBasic').modal('show');
            // html5QrcodeScanner = new Html5QrcodeScanner(
            //     "reader", {
            //         fps: 10,
            //         qrbox: 250
            //     });
            // html5QrcodeScanner.render(onScanSuccess, onScanError);

        }

        function submitBarcode() {
            // temp($('#barcode_input').val());
            onScanSuccess($('#barcode_input').val());
        }

        function showError(error) {
            alert(error);
            // $("#error_text").text(error);
            // $("#error_message_toast").addClass("show");
        }

        function showMessage(message) {
            alert(error);
            // $("#success_text").text(message);
            // $("#success_message_toast").addClass("show");
        }

        $('#barcode_input_new').keypress(function(event) {
            if (event.keyCode == 13) {
                onScanSuccess($('#barcode_input_new').val());
                $('#barcode_input_new').val("");
            }
        });

        @if (isset($item_wise_transfer_order_setting) && $item_wise_transfer_order_setting)
            $('#barcode_scan_itemwise_pick').keypress(function(event) {
                if (event.keyCode == 13) {
                    $('#quantity').attr('readonly','readonly');
                    var item_barcode = $(this).val();
                    if(item_barcode == my_arr[current_item_index]['barcode']){
                        if($('#quantity').val() < my_arr[current_item_index]['quantity'] )
                        {
                            $('#quantity').val( +$('#quantity').val() + 1 );

                            if($('#quantity').val() == my_arr[current_item_index]['quantity']) {
                                $('#to-picked-form').submit();
                            }

                        } else if($('#quantity').val() == my_arr[current_item_index]['quantity']) {
                            $('#to-picked-form').submit();
                        }
                        else{
                            showError("QTY cannot be more than " + my_arr[current_item_index]['quantity']);
                        }
                    }
                    else{
                        showError("Wrong Product");
                    }
                    $('#barcode_scan_itemwise_pick').val("");
                }


            });
        @endif


    </script>
    </div>
    <!-- * App Capsule -->
@endsection
<b></b>
