@extends('layout.mainlayout')
@section('content')
   <!-- App Capsule -->
    <!-- App Capsule -->
    <div id="appCapsule">
    <!-- <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script> -->
   <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
          <!-- toast top iconed -->
<!-- TODO: Basic loaders and everything -->

<!-- Modal Basic -->
<div class="modal fade modalbox" id="ModalBasic" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scan Product</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                    <div class="row">
                        <div class="col">
                            <div style="width:100%" id="reader"></div>
                        </div>

                        </div>



                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Basic -->


          @if(session()->has('message'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
            @if(session()->has('errors'))
            <div id="toast-3" class="toast-box toast-top show">
            @foreach($errors->all() as $error)
                    <div class="in">

                        <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                        <div class="text">
                        {{ $error }}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

             @endforeach
             </div>
            @endif
            @if(Session::has('error'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
        <!-- * toast top iconed -->




        <div class="section full mt-2" >
            <div class="invoice" style="">
                <div class="invoiceBackgroundLogo">
                <img src="" alt="">
                </div>

                <div class="invoice-page-header">

                    <div class="invoice-logo">
                       <div class=""><b>{{$picking_data->product->SKU}}</b>


                        </div>
                    </div>
                    <div class="invoice-id">QTY : <b>{{$picking_data->qty_picked - $picking_data->qty_putaway}}</b></div>
                </div>

                <div class="invoice-person mt-4">
                    <div class="invoice-to">

                    </div>
                    <div class="invoice-from">
                    </div>
                </div>


                <span id="location_input_span"><input type="text" name="location_barcode" placeholder="Scan Location Barcode"  id="location_barcode" autofocus style="width: 100%;" value=""/></span>
                <span id="location_details_span" style="display:none">Stock to be moved on location : <b><span id="location_span"></span></b></span>
            </div>
        </div>

        <div class="section full mt-2" >
            <div class="invoice" style="">
                <div class="invoiceBackgroundLogo">
                    <img src="" alt="">
                </div>


                @if (isset($item_wise_stock_movement_setting) && $item_wise_stock_movement_setting)
                    <div class="input-wrapper" style="margin-bottom: 20px">
                        <input type="text" name="barcode_scan" placeholder="Scan Product Barcode" id="barcode_scan_itemwise_pick" autofocus style="width: 100%;" value="" />
                    </div>
                    <input type="text" readonly name="product_qty" placeholder="Enter QTY"  id="product_qty" onkeyup=enforceMinMax(this) min=1  style="width: 100%;" value=""/>
                @else
                    <input type="text" name="product_qty" placeholder="Enter QTY"  id="product_qty" onkeyup=enforceMinMax(this) min=1 autofocus style="width: 100%;" value=""/>
                @endif

            </div>
        </div>

        <div class="card-body">
            <button type="button" id="submit_button_id" onclick="moveStock()" class="btn btn-primary btn-block btn-lg">Submit</button>
        </div>







    </div>
    <!-- * App Capsule -->
    <div class="toast-box toast-top" id="error_message_toast">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
                    <div class="text" id="error_text">

                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

             </div>

<div id="success_message_toast" class="toast-box toast-top">
    <div class="in">

        <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
        <div class="text" id="success_text">
        </div>
    </div>
    <button type="button" class="btn btn-sm btn-text-error close-button">CLOSE</button>
</div>
<!-- * App Capsule -->

<script>
var product_obj =   {!! json_encode($picking_data->product) !!};
var location_obj = {};
var product_data_received = 0;
var location_data_received = 0;
var session = "{{session('token')}}";

// var html5QrcodeScanner = new Html5QrcodeScanner(
//     "reader", { fps: 10, qrbox: 250 });
// html5QrcodeScanner.render(onLocationScanSuccess, onScanError);

function showModal() {
    $('#barcode_input').val("");
    $("#error_message_toast").removeClass("show");

  $('#ModalBasic').modal('show');
//   html5QrcodeScanner = new Html5QrcodeScanner(
//     "reader", { fps: 10, qrbox: 250 });
//     html5QrcodeScanner.render(onLocationScanSuccess, onScanError);

}



function onScanError(errorMessage) {
  //handle scan error
}

function onLocationScanSuccess(qrCodeMessage) {
    console.log(qrCodeMessage);
    // html5QrcodeScanner.clear();
    $('#ModalBasic').modal('hide');

    //document.getElementById('result').innerHTML = '<span class="result">'+qrCodeMessage+'</span>';
    $(document).ready(function(){
    var Data = new Object();
    // console.log("scannned");
    // console.log(qrCodeMessage);
    Data.location_barcode = qrCodeMessage;
    Data.capacity_required = "{{$picking_data->qty_picked - $picking_data->qty_putaway}}";

    Data.seller_id = 1;

    $.ajax({
            url: '{{env("UNITY_URL")}}/api/ffc/get-location-details-from-barcode-if-capacity',
            data: Data,
            crossDomain: true,
           dataType: 'json',
            type: 'post',
            headers: {
                "Authorization": "Bearer " + session
            },
            success: function(response) {
                 console.log(response);
                if(response.success == true){
                    $('#location_input_span').hide();
                    $('#location_details_span').show();
                    $('#location_span').html(response.data.location.location_name);
                    $("#product_qty").attr({
                        // "max" : response.data.location_capacity,
                        "min" : 1
                        });
                    location_data_received = 1;
                    location_obj = response.data.location;
                }else if(response.success == false){
                    showError(response.message);
                }else{
                    showError("Barcode Invalid");
                }
            },

        });
    });

}



$('#location_barcode').keypress(function(event) {
    if (event.keyCode == 13) {
        onLocationScanSuccess($('#location_barcode').val());
        $('#location_barcode').val("");
    }
});

function showError(error){
    $("#error_text").text(error);
    $("#error_message_toast").addClass("show");
}

function moveStock(){
    $('#submit_button_id').attr("disabled", true);
    console.log(product_obj);
    console.log(location_obj);
    var product_length = Object.keys(product_obj).length;
    var location_length = Object.keys(location_obj).length;
    console.log(product_length);
    console.log(location_length);
    if(location_length > 0){
        $(document).ready(function(){
    var Data = new Object();
    // console.log("scannned");
    // console.log(qrCodeMessage);
    Data.location_id = location_obj.id;
    Data.product_id = product_obj.id;
    Data.quantity = $('#product_qty').val();
        $.ajax({
                url: '{{env("UNITY_URL")}}/api/ffc/move-stock',
                data: Data,
                crossDomain: true,
            dataType: 'json',
                type: 'post',
                headers: {
                    "Authorization": "Bearer " + session
                },
                success: function(response) {
                    console.log(response);
                    if(response.success == true){
                        showError(response.message);
                        setInterval(function () {window.location.href = "/stock-picking";}, 3000);
                    }else{
                        $('#submit_button_id').attr("disabled", false);

                        if(response.success == false){
                            showError(response.message);
                        }else{
                            showError("Something went wrong");
                        }
                    }
                },

            });
        });
    }else{
        $('#submit_button_id').attr("disabled", false);
        showError("Location not scanned or quantity to move is empty");
    }

}

function enforceMinMax(el) {
  if (el.value != "") {
    if (parseInt(el.value) < parseInt(el.min)) {
      el.value = el.min;
    }
    if (parseInt(el.value) > parseInt(el.max)) {
      el.value = el.max;
    }
  }

}



@if (isset($item_wise_stock_movement_setting) && $item_wise_stock_movement_setting)
    $('#barcode_scan_itemwise_pick').keypress(function(event) {
        if (event.keyCode == 13) {
            $('#product_qty').attr('readonly','readonly');
            var item_barcode = $(this).val();
            if(item_barcode == product_obj.barcode){
                $('#product_qty').val( +$('#product_qty').val() + 1 );
            }
            else{
                showError("Wrong Product");
            }
            $('#barcode_scan_itemwise_pick').val("");
        }


    });
@endif

// $(window).bind('beforeunload', function(){
//      return 'Are you sure you want to leave?';
// });

</script>


@endsection
<b></b>


