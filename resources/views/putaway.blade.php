@extends('layout.mainlayout')
@section('content')
   <!-- App Capsule -->
   <div id="appCapsule">
   <!-- <script src="https://unpkg.com/html5-qrcode@2.0.9/dist/html5-qrcode.min.js"></script> -->
   <!-- <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script> -->
   <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

    <!-- Modal Basic -->
    <div class="modal fade modalbox" id="ModalBasic" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scan Product</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                    <div class="row">
                        <div class="col">
                            <div style="width:300px" id="reader"></div>
                        </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Basic -->

    <!-- toast top iconed -->
        
    @if(session()->has('message'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    
                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-error close-button">CLOSE</button>
            </div>

            @endif
            @if(session()->has('errors'))
            {{ $temp=""}}
            @foreach($errors->all() as $error)
            
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    
                    <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
                    <div class="text">
                    {{ $temp = $temp.$error."." }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

             </div>
                @endforeach

            @endif
            @if(Session::has('error'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    
                    <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
                    <div class="text">
                    {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
        <!-- * toast top iconed -->

<form action="/putaway_save" method="POST">
{{ csrf_field() }}

<div class="section mt-2 mb-2">
            <div class="card">
                <ul class="listview flush transparent simple-listview">
                    <li>Product  <span class="text-muted"><input type="text" name="product_barcode" placeholder="Scan Product Barcode" id="product_barcode"  value="{{ old('product_barcode') }}" /></span><span class="iconedbox iconedbox-lg" ><i class="bi bi-upc-scan" onClick="barcodeType(1)"></i></span></li>
                    <li>Location  <span class="text-muted"><input type="text" name="location_barcode" placeholder="Scan Location Barcode" id="location_barcode" value="{{ old('location_barcode') }}" /></span><span class="iconedbox iconedbox-lg"><i class="bi bi-upc-scan" onClick="barcodeType(2)"></i></span></li>
                    <li>QTY &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="text-muted"><input type="number" name="qty" id="qty" value="{{ old('qty') }}" min=0/></span><span class="iconedbox iconedbox-lg" data-bs-toggle="modal" data-bs-target="#ModalBasic"></span></li>
                </ul>
            </div>
        </div>


      

        </div>
        <div class="card-body">         
             <button type="submit" class="btn btn-primary btn-block btn-lg" >Save</button>
        </div>
</form>


<!-- info style-->
<div id="notification-info" class="notification-box">
    <div class="notification-dialog ios-style bg-info">
        <div class="notification-header">
            <div class="in">
                <strong> &nbsp; System</strong>
            </div>
            <div class="right">
                <span>just now</span>
                <a href="#" class="close-button">
                    <ion-icon name="close-circle"></ion-icon>
                </a>
            </div>
        </div>
        <div class="notification-content">
            <div class="in">
                <h3 class="subtitle">Putaway Suggest Location</h3>
                <div class="text">
                    In process.
                </div>
            </div>
            <div class="icon-box text-success">
                <ion-icon name="logo-electron"></ion-icon>
            </div>
        </div>
    </div>
</div>
<!-- * info style -->

<!-- success style-->
<div id="notification-success" class="notification-box">
    <div class="notification-dialog ios-style bg-success">
        <div class="notification-header">
            <div class="in">
                <strong> &nbsp; System</strong>
            </div>
            <div class="right">
                <span>just now</span>
                <a href="#" class="close-button">
                    <ion-icon name="close-circle"></ion-icon>
                </a>
            </div>
        </div>
        <div class="notification-content">
            <div class="in">
                <h3 class="subtitle">Putaway Suggest Location</h3>
                <div class="text">
                    In process.
                </div>
            </div>
            <div class="icon-box text-success">
                <ion-icon name="checkmark-outline"></ion-icon>
            </div>
        </div>
    </div>
</div>
<!-- * success style -->

<!-- danger style-->
<div id="notification-danger" class="notification-box">
    <div class="notification-dialog ios-style bg-danger">
        <div class="notification-header">
            <div class="in">
                <strong> &nbsp; System</strong>
            </div>
            <div class="right">
                <span>just now</span>
                <a href="#" class="close-button">
                    <ion-icon name="close-circle"></ion-icon>
                </a>
            </div>
        </div>
        <div class="notification-content">
            <div class="in">
                <h3 class="subtitle">Putaway Suggest Location</h3>
                <div class="text">
                    
                </div>
            </div>
            <div class="icon-box text-success">
                <ion-icon name="close-outline"></ion-icon>
            </div>
        </div>
    </div>
</div>
<!-- * danger style -->



<script type="text/javascript">
var current_barcode = 0;
function barcodeType(text){
    current_barcode = text;
    $('#ModalBasic').modal('show');
//   html5QrcodeScanner = new Html5QrcodeScanner(
//     "reader", { fps: 10, qrbox: 250 });
//     html5QrcodeScanner.render(onScanSuccess, onScanError);

}

var number = document.getElementById('qty');

// Listen for input event on numInput.
number.onkeydown = function(e) {
    if(!((e.keyCode > 95 && e.keyCode < 106)
      || (e.keyCode > 47 && e.keyCode < 58) 
      || e.keyCode == 8)) {
        return false;
    }
}

$(document).keypress(
  function(event){
    if (event.which == '13') {
      event.preventDefault();
    }
});

function onScanSuccess(qrCodeMessage) {
    //document.getElementById('result').innerHTML = '<span class="result">'+qrCodeMessage+'</span>';
    // html5QrcodeScanner.clear();
    $('#ModalBasic').modal('hide');
    console.log(qrCodeMessage);
    if(current_barcode == 1){
        $('#product_barcode').val(qrCodeMessage);
        suggestLocation(qrCodeMessage);
    }else{
        $('#location_barcode').val(qrCodeMessage);
    }
    
}
function onScanError(errorMessage) {
  //handle scan error
}
// var html5QrcodeScanner = new Html5QrcodeScanner(
//     "reader", { fps: 10, qrbox: 250 });
// html5QrcodeScanner.render(onScanSuccess, onScanError);

function showModal() {
  $('#ModalBasic').modal('show');
//   html5QrcodeScanner = new Html5QrcodeScanner(
//     "reader", { fps: 10, qrbox: 250 });
//     html5QrcodeScanner.render(onScanSuccess, onScanError);

}

function suggestLocation(productBarcode) {
    
    @if (session('putaway_location_suggestion')) 
        
        notification('notification-info');

        $.ajax({
            
            url:'putaway/suggest-location/'+productBarcode,
            
            success: function(data) {

                if (data.error) {
                    $('#notification-danger .text').text(data.message);
                    notification('notification-danger');
                } else {
                    $('#notification-success .text').text(data.message);
                    notification('notification-success');
                    $('#location_barcode').val(data.barcode);
                }
            }
        });
    @endif
}
</script>
</div>
<!-- * App Capsule -->
@endsection
<b></b>