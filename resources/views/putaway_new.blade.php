@extends('layout.mainlayout')
@section('content')
    <!-- App Capsule -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <!-- <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script> -->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
        <!-- toast top iconed -->
        <!-- Modal Basic -->
        <div class="modal fade modalbox" id="ModalBasic" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scan Location</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col">
                                <div style="width:100%" id="reader"></div>
                            </div>

                        </div>



                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Basic -->


        <!-- Modal Basic -->
        <div class="modal fade modalbox" id="ModalLocation" data-bs-backdrop="static" tabindex="-1" role="dialog">


            <div id="ModalLocationLoading" class="toast-box toast-center" style="z-index: 9999">
                <div class="in">
                    <div class="spinner-border text-success" role="status">
                    </div>
                    <div style="margin-top: 5px" class="text">
                        Loading.....
                    </div>
                </div>
            </div>

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scanned Location</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            @if (isset($itemWisePutawaySetting) && $itemWisePutawaySetting == 1)
                                    <input type="text" name="barcode_scan" placeholder="Scan Product Barcode"
                                        id="barcode_scan"  style="width: 100%;" value="" />
                            @endif

                            <div class="col">
                                <div class="wide-block p-0">

                                    <div class="table-responsive">
                                        <form action="/putaway_save_new" method="POST">
                                            {{ csrf_field() }}

                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">#</th>
                                                        <th scope="col">Location</th>
                                                        <th scope="col"> Available Capacity</th>
                                                        <th scope="col"> PutAway QTY</th>

                                                    </tr>
                                                </thead>
                                                <tbody>



                                                    <tr>
                                                        <th scope="row">1</th>
                                                        <td><span id="location_name_span"></span></td>
                                                        <td><span id="location_available_qt_span"></span></td>
                                                        <td>
                                                            @foreach ($pending_putaway_item as $key => $value)
                                                                <input style="width:50px" id="qty_to_putaway_input"
                                                                    type="number" onkeyup=enforceMinMax(this)
                                                                    name="qty" min="0"
                                                                    max="@isset($value->pending_putaway){{$value->pending_putaway}}@endisset"
                                                                    autofocus />
                                                                <input type="hidden" name="location_id" id="location_id" />
                                                                <input type="hidden" name="product_id" name="product_id"
                                                                    value="{{ $value->product_id }}" />

                                                            @endforeach

                                                        </td>




                                                    </tr>

                                                </tbody>
                                            </table>
                                            <div class="card-body" id="save_button_div">
                                                <button type="submit"
                                                    class="btn btn-primary btn-block btn-lg" onclick="$('#save_button_div').hide();$('#ModalLocationLoading').show();">Save</button>

                                            </div>
                                            <input type="hidden" name="stock_order_id" value="" />
                                        </form>
                                    </div>

                                </div>
                            </div>

                        </div>



                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Basic -->



        @if (session()->has('message'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                        {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        @if (session()->has('errors'))
            <div id="toast-3" class="toast-box toast-top show">
                @foreach ($errors->all() as $error)
                    <div class="in">

                        <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                        <div class="text">
                            {{ $error }}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                @endforeach
            </div>
        @endif
        @if (Session::has('error'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">

                    <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                    <div class="text">
                        {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        <!-- * toast top iconed -->
        <div class="section full mt-1 mb-2">
            <!-- <div class="section-title d-flex align-items-center justify-content-center" style="background-color:darkgrey" >

                <div style="width:70%;" id="readers"></div>

                </div> -->
            <div class="section full mt-2">
                <div class="invoice" style="">
                    <div class="invoiceBackgroundLogo">
                        <img src="assets/img/logo.png" alt="">
                    </div>
                    @foreach ($pending_putaway_item as $key => $value)
                        <div class="invoice-page-header">

                            <div class="invoice-logo">
                                <div class="">{{ $key }}
                                </div>
                            </div>
                            <div class="invoice-id">QTY : {{ $value->pending_putaway }}</div>
                        </div>
                    @endforeach

                    <div class="invoice-person mt-4">
                        <div class="invoice-to">

                        </div>
                        <div class="invoice-from">
                        </div>
                    </div>


                    <input type="text" name="barcode_input_new" placeholder="Scan Location Barcode"
                        id="barcode_input_new" autofocus style="width: 100%;" value="" />

                </div>
            </div>
            @if (isset($data['locations']->location_where_capacity_exist->Processing))
                <div class="section full mt-2">
                    <div class="invoice" style="height:40px;padding-top:10px">
                        <div class="invoiceBackgroundLogo">
                            <img src="assets/img/logo.png" alt="">
                        </div>

                        <div class="invoice-page-header" style="display:block">

                            <div class="invoice-logo">
                                <div class="text-center">
                                    Processing

                                </div>
                            </div>
                        </div>

                        <div class="invoice-person mt-4">
                            <div class="invoice-to">

                            </div>
                            <div class="invoice-from">
                            </div>
                        </div>



                    </div>
                </div>

                <div class="wide-block p-0">

                    <div class="table-responsive">
                        <form action="/update-so-details" method="POST">
                            {{ csrf_field() }}

                            <table class="table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Location</th>
                                        <th scope="col"> Available Capacity</th>


                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $counter = 1;
                                    @endphp
                                    @if (isset($data['locations']->location_where_capacity_exist))
                                        @foreach ($data['locations']->location_where_capacity_exist as $key => $value)
                                            @if ($key == 'Processing')
                                                @foreach ($value as $v)
                                                    <tr>
                                                        <th scope="row">{{ $counter }}</th>
                                                        <td>{{ $v->location_name }}</td>
                                                        <td>{{ $v->available_capacity }}</td>



                                                    </tr>
                                                    @php
                                                        $counter++;
                                                    @endphp
                                                @endforeach
                                            @endif
                                        @endforeach
                                    @endif

                                </tbody>
                            </table>
                            <div class="card-body">
                            </div>
                            <input type="hidden" name="stock_order_id" value="" />
                        </form>
                    </div>

                </div>

            @endif
            @if (isset($data['locations']->location_where_capacity_exist->Reserve))

                <div class="section full mt-2">
                    <div class="invoice" style="height:40px;padding-top:10px">
                        <div class="invoiceBackgroundLogo">
                            <img src="assets/img/logo.png" alt="">
                        </div>

                        <div class="invoice-page-header" style="display:block">

                            <div class="invoice-logo">
                                <div class="text-center">
                                    Reserve

                                </div>
                            </div>
                        </div>

                        <div class="invoice-person mt-4">
                            <div class="invoice-to">

                            </div>
                            <div class="invoice-from">
                            </div>
                        </div>



                    </div>
                </div>


                <div class="wide-block p-0">

                    <div class="table-responsive">
                        <form action="/update-so-details" method="POST">
                            {{ csrf_field() }}

                            <table class="table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Location</th>
                                        <th scope="col"> Available Capacity</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $counter = 1;
                                    @endphp
                                    @if (isset($data['locations']->location_where_capacity_exist))
                                        @foreach ($data['locations']->location_where_capacity_exist as $key => $value)
                                            @if ($key == 'Reserve')
                                                @foreach ($value as $v)
                                                    <tr>
                                                        <th scope="row">{{ $counter }}</th>
                                                        <td>{{ $v->location_name }}</td>
                                                        <td>{{ $v->available_capacity }}</td>



                                                    </tr>
                                                    @php
                                                        $counter++;
                                                    @endphp
                                                @endforeach
                                            @endif
                                        @endforeach
                                    @endif

                                </tbody>
                            </table>
                            <div class="card-body">
                            </div>
                            <input type="hidden" name="stock_order_id" value="" />
                        </form>
                    </div>

                </div>
            @endif
        </div>

        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>


        <div class="toast-box toast-center" id="error_message_toast" style="z-index: 9999">
            <div class="in">
                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text" id="error_text">
                </div>
            </div>
        </div>


    </div>
    <!-- * App Capsule -->
    <!-- * App Capsule -->

    <script>
        // var html5QrcodeScanner = new Html5QrcodeScanner(
        //     "reader", {
        //         fps: 10,
        //         qrbox: 250
        //     });
        // html5QrcodeScanner.render(onScanSuccess, onScanError);

        function showModal() {
            $('#barcode_input').val("");
            $("#error_message_toast").removeClass("show");

            $('#ModalBasic').modal('show');
            // html5QrcodeScanner = new Html5QrcodeScanner(
            //     "reader", {
            //         fps: 10,
            //         qrbox: 250
            //     });
            // html5QrcodeScanner.render(onScanSuccess, onScanError);

        }
        var session = "{{ session('token') }}";

        function onScanSuccess(qrCodeMessage) {
            console.log(qrCodeMessage);
            // html5QrcodeScanner.clear();
            $('#ModalBasic').modal('hide');

            //document.getElementById('result').innerHTML = '<span class="result">'+qrCodeMessage+'</span>';
            $(document).ready(function() {
                @if (isset($itemWisePutawaySetting) && $itemWisePutawaySetting == 1)
                $('#qty_to_putaway_input').attr('readonly','readonly');
                @endif
                var Data = new Object();
                // console.log("scannned");
                // console.log(qrCodeMessage);
                Data.location_barcode = qrCodeMessage;

                Data.seller_id = 1;

                $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/get-location-details-from-barcode/',
                    data: Data,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                        //console.log(response);
                        if (response.success == true) {
                            $('#location_name_span').html(response.data.seller_ffc_location.location_name);
                            $('#location_available_qt_span').html(response.data.seller_ffc_location.available_capacity);
                            $("#qty_to_putaway_input").attr({
                                // "max" : response.data.seller_ffc_location.available_capacity,
                                "min": 1
                            });
                            $('#location_id').val(response.data.seller_ffc_location.id);
                            $('#ModalLocation').modal('show');
                            @if (isset($itemWisePutawaySetting) && $itemWisePutawaySetting == 1)
                                setTimeout(function() {  $('#barcode_scan').focus()}, 1000);
                            @endif
                        } else {
                            showError("Barcode Invalid");
                        }
                    },

                });
            });

        }

        function onScanError(errorMessage) {
            //handle scan error
        }

        $('#barcode_input_new').keypress(function(event) {
            if (event.keyCode == 13) {
                if($('#barcode_input_new').val().trim()==""){
                $('#barcode_input_new').val("");
                return
            }
                $('#loading').show();
                onScanSuccess($('#barcode_input_new').val());
                $('#barcode_input_new').val("");
            }
        });

        function showError(error) {
            $("#error_text").text(error);
            toastbox('error_message_toast', 2000);
        }

        function enforceMinMax(el) {
            if (el.value != "") {
                if (parseInt(el.value) < parseInt(el.min)) {
                    el.value = el.min;
                    showError("Value cannot be less than " + el.min);
                }
                if (parseInt(el.value) > parseInt(el.max)) {
                    el.value = el.max;
                    showError("Value cannot be more than " + el.max);
                }
            }

        }

        @if (isset($itemWisePutawaySetting) && $itemWisePutawaySetting == 1)
        $('#barcode_scan').keyup(function(e){
                if(e.keyCode == 13)
                {
                    $('#qty_to_putaway_input').attr('readonly','readonly');
                    enforceMinMax(e);
                    var item_barcode = $(this).val();


                    @foreach ($pending_putaway_item as $key => $value)
                    if(item_barcode=="{{ $value->barcode }}"){
                    @break
                    @endforeach
                        if($('#qty_to_putaway_input').val() <  {{$value->pending_putaway}}){
                                $('#qty_to_putaway_input').val( +$('#qty_to_putaway_input').val() + 1 );
                        }
                        else
                        {
                            showError("QTY cannot be more than {{$value->pending_putaway}}");
                        }

                    }
                    else
                    {
                        showError("Wrong Product");
                    }
                    $('#barcode_scan').val("");
                }
        });
        @endif

    </script>


@endsection
<b></b>
