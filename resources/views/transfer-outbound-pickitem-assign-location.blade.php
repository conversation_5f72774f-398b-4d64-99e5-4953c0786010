@extends('layout.innerlayout')
@section('content')
    <!-- App Capsule -->
    <div id="appCapsule">

        <!-- <script src="https://unpkg.com/html5-qrcode@2.0.9/dist/html5-qrcode.min.js"></script> -->
        <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

        <style>
            .result {
                background-color: green;

            }

            .row {
                display: flex;
            }
        </style>


                <form action="completed" id="item-picked-submit-form" method="POST">

                    @csrf

                    <input type="hidden" class="form-control" id="picked_item" name="picked_item">
                </form>

        <div class="section full mt-1 mb-2">

                <div class="section full mt-3">
                    <div class="invoice" id="picking_item" style="font-size:x-large;">

                            <span id="picking_item_product_name"><b>Product : {{$item_data->product_name}} </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_barcode"><b>Barcode : {{$item_data->barcode}} </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_sku"><b>SKU : {{$item_data->sku}} </b></span>
                            <div> &nbsp;</div>
                            <span id="picking_item_qty">Quantity : {{$item_data->quantity}} </span>
                    </div>
                </div>

            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#accordion003">
                        <ion-icon name="list-outline"></ion-icon>&nbsp;Item FFC Inventories List
                    </button>
                </h2>
                <div id="accordion003" class="accordion-collapse">
                    <div class="accordion-body">
                        
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Location</th>
                                        <th scope="col">QTY</th>
                                        <th scope="col"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <script>
                                        var my_arr = [];
                                    </script>
        
                                    @foreach ($ffc_inventories as $key => $oi)
                                        <script>
                                            my_arr.push({!! json_encode($oi) !!});
                                        </script>
        
                                        <tr @if (isset($oi->picked) && $oi->picked == 1) style="background-color:#00FF00;" @endif >
                                            <th scope="row">{{ $key + 1 }}</th>
                                            <td>
                                                {{ $oi->inventory_path }}

                                                @switch($oi->location_type)
                                                    @case(1)
                                                        <span title="Unsellable" class="badge bg-danger">U</span>
                                                        @break
                                                    @case(2)
                                                        <span title="Processing" class="badge bg-success">P</span>
                                                        @break
                                                    @case(3)
                                                        <span title="Reserve" class="badge bg-info">R</span>
                                                        @break
                                                    @default
                                                        
                                                @endswitch
                                            </td>
                                            <td>{{ $oi->quantity }}</td>
                                            <td id="{{ $oi->inventory_id }}"><button onclick="selectQuantity({{ $oi->quantity }}, {{ $oi->inventory_id }}, {{ $oi->ffc_location_id }}, '{{ $oi->inventory_path }}', {{ $oi->item_id }}, {{ $oi->product_id }})" class="btn btn-sm btn-success">Select</button></td>
                                        </tr>
                                    @endforeach
        
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>

        </div>




    </div>
    <!-- * App Capsule -->


    @if(session()->has('message'))
    <div id="toast-3" class="toast-box toast-top show">
        <div class="in">
            
            <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
            <div class="text">
            {{ session()->get('message') }}
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-error close-button">CLOSE</button>
    </div>

    @endif
    @if(session()->has('errors'))
    {{ $temp=""}}
    @foreach($errors->all() as $error)
    
    <div id="toast-3" class="toast-box toast-top show">
        <div class="in">
            
            <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
            <div class="text">
            {{ $temp = $temp.$error."." }}
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

     </div>
        @endforeach

    @endif
    @if(Session::has('error'))
    <div id="toast-3" class="toast-box toast-top show">
        <div class="in">
            
            <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
            <div class="text">
            {{ Session::get('error') }}
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
    </div>

    @endif
<!-- * toast top iconed -->




        <!-- Modal Form -->
        <div class="modal fade modalbox" id="ModalQuantity" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enter Pick Quantity</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="section mt-4 mb-5">
                                <div class="form-group basic">
                                    <div class="input-wrapper">
                                        <label class="form-label" for="quantity">Quantity</label>
                                        <input type="number" min="1" max="" class="form-control" id="select_quantity" required placeholder="enter quantity">
                                        <i class="clear-input">
                                            <ion-icon name="close-circle"></ion-icon>
                                        </i>
                                    </div>
                                </div>

                                <div class="mt-2">
                                    <button type="submit" onclick="assignLocation()" id="submit_button" class="btn btn-primary btn-block btn-lg">Submit</button>
                                </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Form -->


    <script type="text/javascript">

        var quantity = {{$item_data->quantity}};
        var current_data = {};
        var selected_ffc_inventories = [];
        var max_quantity = 0;


        function selectQuantity(inventory_qty, ffc_inventory_id, ffc_location_id, inventory_path, item_id, product_id) {

            current_data = {};
            current_data.ffc_inventory_id = ffc_inventory_id;
            current_data.ffc_location_id = ffc_location_id;
            current_data.item_id = item_id;
            current_data.product_id = product_id;
            current_data.inventory_path = inventory_path;

            max_quantity = (inventory_qty < quantity ? inventory_qty : quantity);
            $("#select_quantity").attr({"max" : max_quantity});
            $('#ModalQuantity').modal('show');
            $('#submit_button').attr("disabled", false);
        }

        function assignLocation() {

            if ($("#select_quantity").val() >= 1) {
                
                if ($("#select_quantity").val() <= max_quantity) {
                    
                    $('#submit_button').attr("disabled", true);
                    current_data.quantity = $("#select_quantity").val();
                    selected_ffc_inventories.push(current_data);
                    quantity -= current_data.quantity;
                    $('#ModalQuantity').modal('hide');
                    $('#picking_item_qty').html('Quantity : '+quantity);
                    $('#'+current_data.ffc_inventory_id).html(current_data.quantity+' selected');

                    if (!quantity) {
                        $("#picked_item").val( JSON.stringify( selected_ffc_inventories ));
                        $('#item-picked-submit-form').submit();
                    }
                } else {
                    alert('Max quantity is '+max_quantity);
                }

            } else {
                alert('Minimum quantity is 1');
            }
        }

    </script>
@endsection
<b></b>
