@extends('layout.mainlayout')
@section('content')
   <!-- App Capsule -->
    <!-- App Capsule -->
    <div id="appCapsule">
    <!-- <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script> -->
   <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
          <!-- toast top iconed -->



<!-- Modal Basic -->
<div class="modal fade modalbox" id="ModalBasic" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scan Product</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                    <div class="row">
                        <div class="col">
                            <div style="width:100%" id="reader"></div>
                        </div>

                        </div>



                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Basic -->
        <div id="loading" class="toast-box toast-center">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>


        <div class="toast-box toast-center" id="error_message_toast">
            <div class="in">

                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text" id="error_text">

                </div>
            </div>

         </div>


          @if(session()->has('message'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
            @if(session()->has('errors'))
            <div id="toast-3" class="toast-box toast-center show">
            @foreach($errors->all() as $error)
                    <div class="in">
                        <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                        <div class="text">
                        {{ $error }}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

             @endforeach
             </div>
            @endif
            @if(Session::has('error'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                    <div class="text">
                    {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
        <!-- * toast top iconed -->

        <div class="section full mt-2" >
            <div class="invoice" style="">
                <div class="invoiceBackgroundLogo">
                    <img src="" alt="">
                </div>




                <input type="text" name="barcode_input_new" placeholder="Scan Product Barcode"  id="barcode_input_new" autofocus style="width: 100%;" value=""/>

            </div>
        </div>


        <div class="section full mt-1 mb-2">

            <div class="wide-block p-0">

                <div class="table-responsive">
                    {{ csrf_field() }}

                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">SKU</th>
                                <th scope="col"> QTY</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $counter = 1;
                            @endphp
                            @foreach($pending_putaway_items as $key=>$value)
                            <tr>
                            <th scope="row">{{$counter}}</th>
                                <td>{{$value->sku}}</td>
                                <td>{{$value->pending_putaway}}</td>

                                <td> <a href="putaway_new/{{$value->product_id}}">  <button class="btn btn-primary btn-block btn-sm">Put Away</button></a></td>
                            </tr>
                            @php
                                $counter++;
                            @endphp
                            @endforeach

                        </tbody>
                    </table>
                    <div class="card-body">
                  </div>
                </div>

            </div>
        </div>




    </div>
    <!-- * App Capsule -->
<!-- * App Capsule -->

<script>

// var html5QrcodeScanner = new Html5QrcodeScanner(
//     "reader", { fps: 10, qrbox: 250 });
// html5QrcodeScanner.render(onScanSuccess, onScanError);

function showModal() {
    $('#barcode_input').val("");
    $("#error_message_toast").removeClass("show");

  $('#ModalBasic').modal('show');
//   html5QrcodeScanner = new Html5QrcodeScanner(
//     "reader", { fps: 10, qrbox: 250 });
//     html5QrcodeScanner.render(onScanSuccess, onScanError);

}

var session = "{{session('token')}}";

function onScanSuccess(qrCodeMessage) {
    console.log(qrCodeMessage);
    // html5QrcodeScanner.clear();
    $('#ModalBasic').modal('hide');

    //document.getElementById('result').innerHTML = '<span class="result">'+qrCodeMessage+'</span>';
    $(document).ready(function(){
    var Data = new Object();
    // console.log("scannned");
    // console.log(qrCodeMessage);
    Data.product_barcode = qrCodeMessage;

    Data.seller_id = 1;

    $.ajax({
            url: '{{env("UNITY_URL")}}/api/ffc/get-product-details-from-barcode',
            data: Data,
            crossDomain: true,
           dataType: 'json',
            type: 'post',
            headers: {
                "Authorization": "Bearer " + session
            },
            success: function(response) {
                $('#loading').hide();
                // console.log(response);
                if(response.success == true){
                  window.location.href = "/putaway_new/"+response.data.id;
                }else{
                    showError("Barcode Invalid");
                }
            },

        });
    });

}

function onScanError(errorMessage) {
  //handle scan error
}

function showError(error){
    $("#error_text").text(error);
    toastbox('error_message_toast', 2000);
}

$('#barcode_input_new').keypress(function(event) {
        if (event.keyCode == 13) {
            if($('#barcode_input_new').val().trim()==""){
                $('#barcode_input_new').val("");
                return
            }
            $('#loading').show();
            onScanSuccess($('#barcode_input_new').val());
            $('#barcode_input_new').val("");
        }
    });


</script>


@endsection
<b></b>


