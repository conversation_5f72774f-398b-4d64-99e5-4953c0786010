@extends('layout.mainlayout')
@section('content')
    <!-- App Capsule -->
    <div id="appCapsule">



        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>

        <div class="section full mt-1 mb-2">

            <div class="section mb-2">

                <!-- toast top iconed -->

                @if (session()->has('message'))
                    <div id="toast-3" class="toast-box toast-top show">
                        <div class="in">

                            <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                            <div class="text">
                                {{ session()->get('message') }}
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                    </div>
                @endif
                @if (session()->has('errors'))
                    @foreach ($errors->all() as $error)
                        <div id="toast-3" class="toast-box toast-top show">
                            <div class="in">

                                <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                                <div class="text">
                                    {{ $error }}
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                        </div>
                    @endforeach
                @endif


                @if(Session::has('error'))
                <div id="toast-3" class="toast-box toast-top show">
                    <div class="in">
                        
                        <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
                        <div class="text">
                        {{ Session::get('error') }}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                </div>

                @endif
                <!-- * toast top iconed -->

                <div class="wide-block p-0">

                    <div class="table-responsive" style="min-height: 300px">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th scope="col">Order</th>
                                    <th scope="col">SKU</th>
                                    <th scope="col">Total Qty</th>
                                    <th scope="col">Destination</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (count($order_list) > 0)
                                    @foreach ($order_list as $key => $or)
                                        @if ($or->is_picked == 1)
                                            <tr style="background-color:#E8E8E8">
                                        @else
                                            <tr>
                                        @endif
                                            <td>{{ $or->order_ref_id }}</td>
                                            <td>{{ $or->items_count }}</td>
                                            <td>{{ $or->quantity }}</td>
                                            <td>{{ $or->destination_location }}</td>
                                            <td>    
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        Start
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <a href="transfer-outbound/pickitems/{{ $or->order_id }}" onclick="$('#loading').show();" class="dropdown-item" href="#">Manual</a>
                                                        <a href="transfer-outbound/pickitems/{{ $or->order_id }}/1" onclick="$('#loading').show();" class="dropdown-item" href="#">Automated</a>
<!-- FLH-1880 -->                                       <a href="transfer-outbound/pickitems/{{ $or->order_id }}/2" onclick="$('#loading').show();" class="dropdown-item" href="#">Automated (Reserve Locations first)</a 
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td></td>
                                        <td></td>
                                        <td> No Data Found</td>
                                        <td> </td>
                                    </tr>
                                @endif

                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>





    </div>
    <!-- * App Capsule -->

@endsection
<b></b>
