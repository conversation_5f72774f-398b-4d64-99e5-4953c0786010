@extends('layout.plain')
@section('content')
    <!-- App Capsule -->
    <div id="appCapsule" class="pt-0">
        <!-- TODO: put loader on submit -->
        <div class="login-form mt-1">
            <div class="section">
                <img src="{{ asset ("assets/img/flash.png") }}" alt="image" class="form-image">
            </div>
            <br>
            <br><br><br><br><br><br>
            <div class="section mt-1">
                <h1>Get started</h1>
                <h4>Fill the form to log in</h4>
            </div>
            <div class="section mt-1 mb-5">
                <form action="/auth" method="POST">
                {{ csrf_field() }}

                    <div class="form-group boxed">
                        <div class="input-wrapper">
                            <input type="email" class="form-control" autofocus id="email" name="email" placeholder="Email address">
                            <i class="clear-input">
                                <ion-icon name="close-circle"></ion-icon>
                            </i>
                        </div>
                    </div>

                    <div class="form-group boxed">
                        <div class="input-wrapper">
                            <input type="password" class="form-control" id="password" name="password" placeholder="Password" autocomplete="off">
                            <i class="clear-input">
                                <ion-icon name="close-circle"></ion-icon>
                            </i>
                        </div>
                    </div>

                    <div class="form-links mt-2">
                        <div>
                            <a href="page-register.html">&nbsp;</a>
                        </div>
                        <div><a href="page-forgot-password.html" class="text-muted">Forgot Password?</a></div>
                    </div>

                    <div class="form-button-group">
                        <button type="submit" class="btn btn-primary btn-block btn-lg">Log in</button>
                    </div>

                </form>
            </div>
        </div>

                    @if(session()->has('errors'))
                        <div class="alert alert-danger">
                            <ul>
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if(Session::has('error'))

                            <div class="alert alert-danger">
                            <ul>
                                    <li>{{ Session::get('error') }}</li>
                            </ul>
                        </div>
                    @endif


    </div>
    <!-- * App Capsule -->
@endsection
<b></b>
