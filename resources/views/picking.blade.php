@extends('layout.mainlayout')
@section('content')
    <!-- App Capsule -->
    <div id="appCapsule">


        @if(session()->has('message'))
        <div id="toast-3" class="toast-box toast-center show">
            <div class="in">

                <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                <div class="text">
                {{ session()->get('message') }}
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
        </div>

        @endif
        @if(session()->has('errors'))
        <div id="toast-3" class="toast-box toast-center show">
        @foreach($errors->all() as $error)
                <div class="in">
                    <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                    <div class="text">
                    {{ $error }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

         @endforeach
         </div>
        @endif
        @if(Session::has('error'))
        <div id="toast-3" class="toast-box toast-center show">
            <div class="in">

                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text">
                {{ Session::get('error') }}
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
        </div>

        @endif

        <div class="section full mt-1 mb-2">

            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">Orders Not Assigned Yet!</h5>
                    <p class="card-text">Click the button below to assign orders to yourself.</p>
                    <a href="/assign-picklist" class="btn btn-lg btn-primary" id="assign-pick-list" onclick="javascript:$('#assign-pick-list').hide();$('#assign-pick-list-loader').show();">Assign Pick List</a>
                    <div class="spinner-border text-primary" id="assign-pick-list-loader" role="status" style="display:none"></div>
                </div>

            </div>
        </div>
    </div>
    <!-- * App Capsule -->

    <div class="toast-box toast-center" id="error_message_toast" style="z-index: 9999">
        <div class="in">
            <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
            <div class="text" id="error_text">
            </div>
        </div>
     </div>
    </div>
    <!-- * App Capsule -->

@endsection
<b></b>
