@extends('layout.innerlayout')
@section('content')
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

    <style>
        .floating-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background-color: #6457A6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        .floating-btn ion-icon {
            font-size: 24px;
        }
    </style>

    <!-- TODO: Error and everything to be sorted -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <!-- toast top iconed -->



        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>

        <div id="jsSuccess" class="toast-box toast-center">
            <div class="in">

                <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                <div class="text">
                    <span id="js_success_message"></span>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button" onclick="location.href='/sodetails/{{$so_id}}'">CLOSE</button>
        </div>

        <div id="jsError" class="toast-box toast-center">
            <div class="in">

                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text">
                   <span id="js_error_message"></span>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button" onclick="javascript:$('#jsError').hide();">CLOSE</button>
        </div>



        <div class="modal fade modalbox" id="ModalLocation" data-bs-backdrop="static" tabindex="-1" role="dialog">

            <div id="ModalLocationLoading" class="toast-box toast-center" style="z-index: 9999">
                <div class="in">
                    <div class="spinner-border text-success" role="status">
                    </div>
                    <div style="margin-top: 5px" class="text">
                        Loading.....
                    </div>
                </div>
            </div>


            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enter Putaway Location</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="section mt-4 mb-5">
                            <div class="form-group basic">

                                <div class="input-wrapper" style="margin-bottom: 20px">
                                    <input type="text" name="location_barcode_scan" placeholder="Scan Location Barcode" id="location_barcode_scan" style="width: 100%;" value="" />
                                </div>
                            </div>

                            <div class="mt-2">
                                <button type="button" onclick="checkLocation()" class="btn btn-primary btn-block btn-lg">Submit</button>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (session()->has('message'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text"> {{ session()->get('message') }} </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        @if (session()->has('errors'))
            <div id="toast-3" class="toast-box toast-top show">
                @foreach ($errors->all() as $error)
                    <div class="in">
                        <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                        <div class="text">{{ $error }}</div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                @endforeach
            </div>
        @endif
        @if (Session::has('error'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">{{ Session::get('error') }}</div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        <!-- * toast top iconed -->








        <div class="section full mt-1 mb-2">
            <div style="text-align:center;color:red">
                Total Items Scanned: <span id="total_so_count">0</span> / <span id="total_qty_to_be_scanned">0</span>
            </div>

            <div class="section full mt-2">
                <div class="invoice" style="">
                    <div class="invoiceBackgroundLogo">
                        <img src="" alt="">
                    </div>
                    <input type="text" name="barcode_scan" placeholder="Scan Product Barcode" id="barcode_scan" autofocus style="width: 100%;" value="" />
                </div>
            </div>

            <div id="scan_status_div" style="padding-left:10px;color:red;display:none">
                Last Item Scanned: <span id="scan_status_span"></span>
            </div>

            <div id="over_delivery_error" style="padding-left:10px;color:red;display:none"> Over delivery is not allowed! </div>

            <div class="wide-block p-0">

                <div class="table-responsive">
                    <form id="ajax-form"  method="POST">
                        {{ csrf_field() }}
                        <input type="hidden" name="unique_id" value="{{$unique_id}}">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">SKU</th>
                                    <th scope="col">Expected QTY</th>
                                    <th scope="col">Remaining QTY</th>
                                    <th scope="col">QTY</th>
                                </tr>
                            </thead>

                            <tbody>
                                @php $total_qty_to_be_scanned = 0; @endphp

                                @foreach ($stock_order_items as $so_key => $so)

                                    @if ($so->qty - $so->qty_received <= 0)
                                        <tr id="{{ $barcodes_arr[$so->barcode]['counter'] . '_row' }}" style="background-color:rgba(46, 220, 80, 0.8)">
                                    @else
                                        <tr id="{{ $barcodes_arr[$so->barcode]['counter'] . '_row' }}">
                                    @endif

                                            <th scope="row">{{$so_key+1}}</th>
                                            <td id="order-item-sku-{{ $so->id }}">{{ $so->sku }}</td>
                                            <td>{{ $so->qty }}</td>

                                            @php

                                                if ($so->qty_received <= $so->qty) {
                                                    $total_qty_to_be_scanned = $total_qty_to_be_scanned + ($so->qty - $so->qty_received);
                                                } else {
                                                    $total_qty_to_be_scanned = $total_qty_to_be_scanned + 0;
                                                }

                                            @endphp

                                            @if ($so->qty_received <= $so->qty)
                                                <td>{{ $so->qty - $so->qty_received }}</td>
                                            @else
                                                <td>0</td>
                                            @endif

                                            {{-- <input style="width:40px" type="hidden" id="order-item-remaining-hidden-{{ $so->id }}" name="qty[{{ $so->id }}]" value="0" /> --}}

                                            <td>
                                                <span id="order-item-remaining-{{ $so->id }}">0</span> /

                                                @if ($so->qty_received <= $so->qty)
                                                    <span id="order-item-total-{{ $so->id }}">{{ $so->qty - $so->qty_received }} </span>
                                                @else
                                                    <span id="order-item-total-{{ $so->id }}">0</span>
                                                @endif

                                            </td>

                                        </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="card-body" id="save_button_div">
                            <button type="submit" id="save_button_id" disabled="true" class="btn btn-primary btn-block btn-lg" onclick="javascript:$('#save_button_div').hide();$('#save-items-receive-loader').show(); $('#loading').show();">Receive Only</button>
                            <hr>
                            <button type="button" id="save_button_id2" disabled="true" onclick="receivedAndPutaway()" class="btn btn-primary btn-block btn-lg">Receive and Putaway</button>
                        </div>
                        <input type="hidden" name="putaway_location" id="putaway_location" />
                        <input type="hidden" name="order_items_scanned" id="order_items_scanned" />
                        <input type="hidden" name="stock_order_id" value="{{ $so_id }}" />
                    </form>
                </div>

            </div>
        </div>




    </div>
    <!-- * App Capsule -->
    <!-- * App Capsule -->

    <!-- Scroll to Bottom Button -->
    <div id="scrollToBottomBtn" class="floating-btn" style="display: none;">
        <ion-icon name="arrow-down-outline"></ion-icon>
    </div>
   <!-- Scroll to Bottom Button -->


    <script>
            $(document).ready(function() {
                $('#ajax-form').on('submit', function(e) {
                e.preventDefault(); // Prevent the default form submission

                // Get the form data
                var formData = $(this).serialize();
                console.log(formData);

                $.ajax({
                    url: "{{ route('update-so-details-name-ajax') }}", // Route to handle the form submission
                    type: "POST",
                    data: formData,
                    timeout: 30000, //
                    success: function(response) {
                        // Handle the response
                        console.log(response);
                        if(response.message_id == "Success"){
                            $('#js_success_message').html(response.message);
                            $('#jsSuccess').show();
                            $('#loading').hide();
                        }

                        if(response.message_id == "Error"){
                            $('#js_error_message').html(response.message);
                            $('#jsError').show();
                            $('#loading').hide();
                        }
                    },
                    error: function(xhr, status, error) {

                        if (status === 'timeout') {
                            $('#js_error_message').html("Request timed out. Please try again.");
                            $('#jsError').show();
                            $('#loading').hide();
                            $('#ModalLocation').modal('hide');
                            $('#save_button_id').attr('disabled', false);
                            $('#save_button_id2').attr('disabled', false);
                            $('#save_button_div').show();
                        } else {
                            $('#js_error_message').html("xhr.responseText");
                            $('#jsError').show();
                            $('#loading').hide();
                            $('#ModalLocation').modal('hide');
                            $('#save_button_id').attr('disabled', false);
                            $('#save_button_id2').attr('disabled', false);
                            $('#save_button_div').show();
                        }
                     
                        console.log(xhr.responseText);
                    }
                });
            });



            $('#scrollToBottomBtn').on('click', function() {
                $('html, body').animate({
                    scrollTop: $(document).height()
                }, 800);
            });

            // Check on page load
            checkContentHeight();

            // Check on window resize
            $(window).resize(function() {
                checkContentHeight();
            });

            // Check on scroll
            $(window).scroll(function() {
                checkContentHeight();
            });
        });

        var scanned_barcode_arr = [];
        var barcodes_array = @json($barcodes_arr);
        let over_delivery_settings = @json($over_delivery_settings);
        let total_qty_to_be_scanned = @json($total_qty_to_be_scanned);
        var so_items_scanned = {};


        $(document).ready(function() {



            $('#total_qty_to_be_scanned').html(total_qty_to_be_scanned);
            $('#barcode_scan').keyup(function(e) {
                if (e.keyCode == 13) {
                    $('#over_delivery_error').hide();
                    $('#scan_status_div').show();
                    var item_barcode = $(this).val();
                    if (item_barcode in barcodes_array) {

                        console.log(item_barcode);
                        console.log(barcodes_array);
                        // if($('#'+barcodes_array[item_barcode]['counter']).text() == ''){
                        $('#' + barcodes_array[item_barcode]['counter']).html(item_barcode);
                        var scanned_qty = $('#order-item-remaining-' + barcodes_array[item_barcode]['order_item_id']).html();
                        var total_qty = $('#order-item-total-' + barcodes_array[item_barcode]['order_item_id']).html();
                        var total_scanned = $('#total_so_count').html();
                        var sku = $('#order-item-sku-' + barcodes_array[item_barcode]['order_item_id']).html();
                        
                        $('#scan_status_div').css("color", "#37c22e");

                        console.log(scanned_qty);
                        scanned_qty = parseInt(scanned_qty);
                        total_qty = parseInt(total_qty);
                        total_scanned = parseInt(total_scanned);
                        scanned_qty = scanned_qty + 1;
                        total_scanned = total_scanned + 1;
                        $('#scan_status_span').html(sku + " (" + item_barcode + ")" + " - (" + scanned_qty + "/" + $('#order-item-total-' + barcodes_array[item_barcode]['order_item_id']).html() + ")");

                        if (over_delivery_settings == 1) {
                            $('#order-item-remaining-' + barcodes_array[item_barcode]['order_item_id']) .html(scanned_qty);
                            $('#total_so_count').html(total_scanned);
                            // $('#order-item-remaining-hidden-' + barcodes_array[item_barcode][ 'order_item_id']).val(scanned_qty);
                            so_items_scanned['qty-'+barcodes_array[item_barcode]['order_item_id']] = scanned_qty;
                            $('#order_items_scanned').val(JSON.stringify(so_items_scanned));
                            $('#save_button_id').attr('disabled', false);
                            $('#save_button_id2').attr('disabled', false);

                        } else {
                            if (scanned_qty > total_qty) {
                                play();
                                $('#over_delivery_error').show();
                            } else {
                                $('#order-item-remaining-' + barcodes_array[item_barcode]['order_item_id']).html(scanned_qty);
                                $('#total_so_count').html(total_scanned);
                                // $('#order-item-remaining-hidden-' + barcodes_array[item_barcode]['order_item_id']).val(scanned_qty);
                                so_items_scanned['qty-'+barcodes_array[item_barcode]['order_item_id']] = scanned_qty;
                                $('#order_items_scanned').val(JSON.stringify(so_items_scanned));
                                $('#save_button_id').attr('disabled', false);
                                $('#save_button_id2').attr('disabled', false);
                            }
                        }

                        if (total_qty == scanned_qty) {
                            $('#' + barcodes_array[item_barcode]['counter'] + '_row').css("background-color", "rgba(46, 220, 80, 0.8)");
                            scanned_barcode_arr.push(item_barcode);
                            if (scanned_barcode_arr.length == @json(count($stock_order_items))) {
                                // $('#save_button_div').prop('hidden',false);
                            }
                        }


                        // }

                    } else {

                        $('#scan_status_div').css("color", "red");
                        $('#scan_status_span').html(item_barcode + " : Barcode does not exist in the list , now scanning for carton) ");
                        play();
                        
                        scanCarton(item_barcode);

                    }
                    $(this).val("");

                }

            })

        });

        var form_submitted = false;


        function showError(error) {
            $("#error_text").text(error);
            $("#error_message_toast").addClass("show");
        }

        function showMessage(message) {
            $("#error_text").text(message);
            $("#error_message_toast").addClass("show");
        }


        function receivedAndPutaway() {
            $('#ModalLocation').modal('show');
        }

        $('#location_barcode_scan').keyup(e => {
            if (e.keyCode == 13) {
                checkLocation();
            }
        });

        



        function play() {
            var audio = new Audio('https://media.geeksforgeeks.org/wp-content/uploads/20190531135120/beep.mp3');
            audio.play();
        }


        function checkLocation() {
            let location_barcode = $('#location_barcode_scan').val();

            if (location_barcode) {

                if (!form_submitted) {

                    var Data = new Object();
                    Data.location_barcode = location_barcode;
                    Data.seller_id = 1;

                    $('#ModalLocationLoading').show();

                    $.ajax({
                        url: '{{ env('UNITY_URL') }}/api/ffc/get-location-details-from-barcode/',
                        data: Data,
                        crossDomain: true,
                        dataType: 'json',
                        type: 'post',
                        headers: {
                            "Authorization": "Bearer " + "{{ session('token') }}",
                        },
                        success: function(response) {

                            if (response.success == true) {

                                if (!form_submitted) {

                                    form_submitted = true;
                                    $('#loading').show();
                                    $('#putaway_location').val(response.data.seller_ffc_location.id);
                                    $('#ModalLocation').modal('hide');
                                    $('#ajax-form').submit();
                                }

                            } else {
                                $('#ModalLocationLoading').hide();
                                alert("Barcode Invalid");
                            }
                        },

                    });
                }

            } else {
                alert("Enter location barcode first");
            }
        }


        function scanCarton(barcode)
        {
            $('#loading').show()
            var Data = new Object();
            Data.barcode = barcode;
            Data.so_items_scanned = so_items_scanned;
            Data.so_id = {{ $so_id }};
            Data._token = '{{ csrf_token() }}';

            $.ajax({
                url: '/sodetails/scan-carton-barcode',
                data: Data,
                dataType: 'json',
                type: 'post',
                success: function(response) {

                    $('#loading').hide();


                    if (response.error) {

                        $('#scan_status_span').html(barcode + " : Barcode does not exist in the list , "+response.message+" ) ");
                        play();

                    } else {

                        response.data.forEach(element => {
                            

                            var item_barcode = element.product.barcode;
                            if (item_barcode in barcodes_array) {

                                console.log(item_barcode);
                                console.log(barcodes_array);
                                // if($('#'+barcodes_array[item_barcode]['counter']).text() == ''){
                                $('#' + barcodes_array[item_barcode]['counter']).html(item_barcode);
                                var scanned_qty = $('#order-item-remaining-' + barcodes_array[item_barcode]['order_item_id']).html();
                                var total_qty = $('#order-item-total-' + barcodes_array[item_barcode]['order_item_id']).html();
                                var total_scanned = $('#total_so_count').html();
                                var sku = $('#order-item-sku-' + barcodes_array[item_barcode]['order_item_id']).html();
                                
                                $('#scan_status_div').css("color", "#37c22e");

                                console.log(scanned_qty);
                                scanned_qty = parseInt(scanned_qty);
                                total_qty = parseInt(total_qty);
                                total_scanned = parseInt(total_scanned);
                                scanned_qty = scanned_qty + element.quantity;
                                total_scanned = total_scanned + element.quantity;
                                $('#scan_status_span').html("All items in Carton scanned "+barcode+" successfully");

                                if (over_delivery_settings == 1) {
                                    $('#order-item-remaining-' + barcodes_array[item_barcode]['order_item_id']) .html(scanned_qty);
                                    $('#total_so_count').html(total_scanned);
                                    // $('#order-item-remaining-hidden-' + barcodes_array[item_barcode][ 'order_item_id']).val(scanned_qty);
                                    so_items_scanned['qty-'+barcodes_array[item_barcode]['order_item_id']] = scanned_qty;
                                    $('#order_items_scanned').val(JSON.stringify(so_items_scanned));
                                    $('#save_button_id').attr('disabled', false);
                                    $('#save_button_id2').attr('disabled', false);

                                } else {
                                    if (scanned_qty > total_qty) {
                                        play();
                                        $('#over_delivery_error').show();
                                    } else {
                                        $('#order-item-remaining-' + barcodes_array[item_barcode]['order_item_id']).html(scanned_qty);
                                        $('#total_so_count').html(total_scanned);
                                        // $('#order-item-remaining-hidden-' + barcodes_array[item_barcode]['order_item_id']).val(scanned_qty);
                                        so_items_scanned['qty-'+barcodes_array[item_barcode]['order_item_id']] = scanned_qty;
                                        $('#order_items_scanned').val(JSON.stringify(so_items_scanned));
                                        $('#save_button_id').attr('disabled', false);
                                        $('#save_button_id2').attr('disabled', false);
                                    }
                                }

                                if (total_qty == scanned_qty) {
                                    $('#' + barcodes_array[item_barcode]['counter'] + '_row').css("background-color", "rgba(46, 220, 80, 0.8)");
                                    scanned_barcode_arr.push(item_barcode);
                                    if (scanned_barcode_arr.length == @json(count($stock_order_items))) {
                                        // $('#save_button_div').prop('hidden',false);
                                    }
                                }


                            } else {

                                $('#scan_status_div').css("color", "red");
                                $('#scan_status_span').html(item_barcode + " : Carton Barcode does not exist in the list) ");
                                play();

                            }

                        });
                        
                    }
                },

            });
                

        }

        // $(function() {
        //     $(window).bind('beforeunload', function() {
        //         setTimeout(function() {
        //             setTimeout(function() {
        //                 $('#save_button_id').attr('disabled', false);
        //             }, 1000);
        //         },1);
        //         return 'Are you sure you want to leave?';
        //     });
        // }); 



        // Function to check if content is taller than viewport
        function checkContentHeight() {
            const documentHeight = $(document).height();
            const windowHeight = $(window).height();
            const scrollBtn = $('#scrollToBottomBtn');

            if (documentHeight > windowHeight + 100) { // Adding buffer of 100px
                scrollBtn.show();
            } else {
                scrollBtn.hide();
            }

            // Also hide button if already at the bottom
            if ($(window).scrollTop() + windowHeight >= documentHeight - 50) {
                scrollBtn.hide();
            }
        }
    </script>

@endsection
<b></b>
