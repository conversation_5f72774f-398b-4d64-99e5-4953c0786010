@extends('layout.mainlayout')
@section('content')
    <!-- App Capsule -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="fab-button text bottom-center" style="margin-bottom:80px; display:none;" id="btn_send_for_picking">
            <a href="#" class="fab" onclick="savePicking()">
                <ion-icon name="add-outline" role="img" class="md hydrated" aria-label="add outline"></ion-icon>
                Picked
            </a>
        </div>
        <div class="fab-button text bottom-center" style="margin-bottom:80px; display:none;"
            id="btn_send_for_picking_putaway">
            <a href="#" class="fab" onclick="savePutaway()">
                <ion-icon name="add-outline" role="img" class="md hydrated" aria-label="add outline"></ion-icon>
                Putaway
            </a>
        </div>
        <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
        <!-- toast top iconed -->


        <!-- START: Bulk QTY Modal -->
        <div class="modal fade dialogbox show" id="ModalManualQTY" data-bs-backdrop="static" tabindex="-1" role="dialog"
            aria-modal="true" style="display: none;">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="barcodeScannedManualQTY"></h5>
                    </div>
                    <form>
                        <div class="modal-body text-start mb-2">

                            <div class="form-group basic">
                                <div class="input-wrapper">
                                    <input type="number" min="0" max="9999"class="form-control" id="barcodeManualQTY"
                                        placeholder="Enter QTY" autocomplete="off" onkeyup=enforceMinMax(this)>
                                    <i class="clear-input">
                                        <ion-icon name="close-circle" role="img" class="md hydrated"
                                            aria-label="close circle"></ion-icon>
                                    </i>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="btn-inline">
                                <button type="button" class="btn btn-text-secondary"
                                    data-bs-dismiss="modal">CANCEL</button>
                                <button type="button" class="btn btn-text-primary" data-bs-dismiss="modal"
                                    id="ModalManualQTYSubmit" onclick="barcodeManualQtyProcess()">SUBMIT</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- END: Bulk QTY Modal -->


        <!-- START: Bulk QTY Modal -->
        <div class="modal fade dialogbox show" id="ModalManualQTYPutaway" data-bs-backdrop="static" tabindex="-1" role="dialog"
            aria-modal="true" style="display: none;">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="barcodeScannedManualQTYPutaway"></h5>
                    </div>
                    <form>
                        <div class="modal-body text-start mb-2">

                            <div class="form-group basic">
                                <div class="input-wrapper">
                                    <input type="number" min="0" max="9999"class="form-control" id="barcodeManualQTYPutaway"
                                        placeholder="Enter QTY" autocomplete="off" onkeyup=enforceMinMax(this)>
                                    <i class="clear-input">
                                        <ion-icon name="close-circle" role="img" class="md hydrated"
                                            aria-label="close circle"></ion-icon>
                                    </i>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="btn-inline">
                                <button type="button" class="btn btn-text-secondary"
                                    data-bs-dismiss="modal">CANCEL</button>
                                <button type="button" class="btn btn-text-primary" data-bs-dismiss="modal"
                                    id="ModalManualQTYSubmitPutaway" onclick="barcodeManualQtyProcessPutaway()">SUBMIT</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- END: Bulk QTY Modal -->


        <!-- TODO: Basic loaders and everything -->
        <!-- Modal Basic -->
        <div class="tab-pane fade show" id="lined" role="tabpanel">

            <div class="section full mt-1">
                <div class="wide-block pb-2">

                    <ul class="nav nav-tabs lined" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#home11" role="tab">
                                Picking
                            </a>
                        </li>
                        <li class="nav-item ">
                            <a class="nav-link " data-bs-toggle="tab" href="#profile12" role="tab">
                                Trolley Contents
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#contact13" role="tab">
                                Putaway
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content mt-2">
                        <div class="tab-pane fade show active" id="home11" role="tabpanel">
                            <div class="section full mt-1 mb-2">

                                <div class="section full mt-2" id="location_scan_field">
                                    <div class="invoice" style="">
                                        <input type="text" name="location_barcode_scan"
                                            placeholder="Scan Location Barcode" id="location_barcode_scan" autofocus
                                            style="width: 100%;" value="" />
                                    </div>
                                </div>
                                <div class="card text-white bg-success mb-2" id="location_card"
                                    style="display: none; text-align:center; margin-left:10px;margin-right:10px;">
                                    <div class="card-header"><span id="location_name"></span></div>
                                    <!-- <p class="card-header"><span id="location_capacity"></span></p> -->
                                </div>
                                <div class="section full mt-2" id="product_scan_field" style="display:none;">
                                    <div class="invoice" style="">
                                        <input type="text" name="product_barcode_scan"
                                            placeholder="Scan Product Barcode"id="product_barcode_scan" autofocus
                                            style="width: 100%;" value="" />
                                    </div>
                                </div>
                                <div class="wide-block p-0" id="products_grid_div" style="display: none";>

                                    <div class="table-responsive">
                                        <form action="/update-so-details" method="POST">
                                            {{ csrf_field() }}

                                            <span class="p-2" id="total_count"></span>

                                            <table class="table" id="products_grid">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">SKU</th>
                                                        <th scope="col">Barcode</th>
                                                        <th scope="col">QTY</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                </tbody>
                                            </table>
                                            <div class="card-body">
                                            </div>
                                            <input type="hidden" name="stock_order_id" value="" />
                                        </form>
                                    </div>

                                </div>

                            </div>

                        </div>
                        <div class="tab-pane fade" id="profile12" role="tabpanel">
                            <div class="wide-block p-0">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">Bin</th>
                                                <th scope="col">Barcode</th>
                                                <th scope="col">SKU</th>
                                                <th scope="col">QTY</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (isset($picking_data) && isset($picking_data->products) && count($picking_data->products) > 0)
                                                @php
                                                    $count = 1;
                                                @endphp
                                                @foreach ($picking_data->products as $pr)
                                                    <tr>
                                                        <th scope="row">{{ $count }}</th>
                                                        <td>{{ $pr->location_name }}</td>
                                                        <td>{{ $pr->product->barcode }}</td>
                                                        <td>{{ $pr->product->SKU }}</td>
                                                        <td>{{ $pr->qty_picked - $pr->qty_putaway }}</td>
                                                    </tr>
                                                    @php
                                                        $count++;
                                                    @endphp
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td></td>
                                                    <td></td>
                                                    <td> No Data Found</td>
                                                    <td> </td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                        <div class="tab-pane fade" id="contact13" role="tabpanel">
                            <div class="section full mt-1 mb-2">

                                <div class="section full mt-2" id="location_scan_field_putaway">
                                    <div class="invoice" style="">
                                        <input type="text" name="location_barcode_scan"
                                            placeholder="Scan Location Barcode" id="location_barcode_scan_putaway"
                                            autofocus style="width: 100%;" value="" />
                                    </div>
                                </div>
                                <div class="card text-white bg-success mb-2" id="location_card_putaway"
                                    style="display: none; text-align:center; margin-left:10px;margin-right:10px;">
                                    <div class="card-header"><span id="location_name_putaway"></span></div>
                                    <!-- <p class="card-header"><span id="location_capacity_putaway"></span></p> -->
                                </div>
                                <div class="section full mt-2" id="product_scan_field_putaway" style="display:none;">
                                    <div class="invoice" style="">
                                        <input type="text" name="product_barcode_scan_putaway"
                                            placeholder="Scan Product Barcode"id="product_barcode_scan_putaway" autofocus
                                            style="width: 100%;" value="" />
                                    </div>
                                </div>
                                <div class="wide-block p-0" id="products_grid_div_putaway" style="display: none";>

                                    <div class="table-responsive">
                                        <form action="/update-so-details" method="POST">
                                            {{ csrf_field() }}

                                            <span class="p-2" id="total_count"></span>

                                            <table class="table" id="products_grid_putaway">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">SKU</th>
                                                        <th scope="col">Barcode</th>
                                                        <th scope="col">Picked / Putaway</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                </tbody>
                                            </table>
                                            <div class="card-body">
                                            </div>
                                            <input type="hidden" name="stock_order_id" value="" />
                                        </form>
                                    </div>

                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="modal fade modalbox" id="ModalBasic" data-bs-backdrop="static" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scan Product</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col">
                                <div style="width:100%" id="reader"></div>
                            </div>

                        </div>



                    </div>
                </div>
            </div>
        </div>
        <!-- * Modal Basic -->

        <div id="jsSuccess" class="toast-box toast-center">
            <div class="in">

                <ion-icon name="checkmark-circle" class="text-success" id="successIcon"></ion-icon>
                <ion-icon name="alert-circle" class="text-warning" id="warningIcon" style="display: none;"></ion-icon>
                <div class="text">
                    <span id="js_success_message"></span>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button"
                onclick="location.href='/stock-picking'">CLOSE</button>
        </div>

        <div id="jsError" class="toast-box toast-center">
            <div class="in">

                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text">
                    <span id="js_error_message"></span>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button"
                onclick="javascript:$('#jsError').hide();">CLOSE</button>
        </div>

        @if (session()->has('message'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                        {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        @if (session()->has('errors'))
            <div id="toast-3" class="toast-box toast-top show">
                @foreach ($errors->all() as $error)
                    <div class="in">

                        <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                        <div class="text">
                            {{ $error }}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                @endforeach
            </div>
        @endif
        @if (Session::has('error'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                        {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        <!-- * toast top iconed -->








    </div>
    <!-- * App Capsule -->
    <div id="loading" class="toast-box toast-center" style="z-index: 9999">
        <div class="in">
            <div class="spinner-border text-success" role="status">
            </div>
            <div style="margin-top: 5px" class="text">
                Loading.....
            </div>
        </div>
    </div>

    <div class="toast-box toast-top" id="error_message_toast">
        <div class="in">

            <ion-icon name="checkmark-circle" class="text-error"></ion-icon>
            <div class="text" id="error_text">

            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

    </div>

    <div id="success_message_toast" class="toast-box toast-top">
        <div class="in">

            <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
            <div class="text" id="success_text">
            </div>
        </div>
        <button type="button" class="btn btn-sm btn-text-error close-button">CLOSE</button>
    </div>
    <!-- * App Capsule -->

    <script>
        var product_obj = {};
        var location_obj = {};
        var product_data_received = 0;
        var location_data_received = 0;
        var session = "{{ session('token') }}";

        var bin_inventories = [];

        var picking_data = new Object();
        picking_data.products = [];
        var putaway_data = new Object();
        putaway_data.products = [];

        var trolley_contents_for_putaway = @json(isset($products_for_putaway) ? $products_for_putaway : []);


        function enforceMinMax(el) {
            if (el.value != "") {
                if (parseInt(el.value) < parseInt(el.min)) {
                    el.value = el.min;
                }
                if (parseInt(el.value) > parseInt(el.max)) {
                    el.value = el.max;
                }
            }

        }

        function onProductScanSuccess(qrCodeMessage) {

            $('#product_barcode_scan').attr('disabled', true);

            var barcode = qrCodeMessage;
            var qty = 1;

            if (bin_inventories && bin_inventories.length > 0) {
                // Search for the barcode and check uncommitted_qty
                var found = bin_inventories.find(function(inventory) {
                    return inventory.barcode === barcode;
                });

                if (found) {
                    if (qty <= found.uncommitted_qty) {
                        // Deduct the qty
                        found.uncommitted_qty -= qty;
                        $('#products_grid_div').show();
                        $('#btn_send_for_picking').show();
                        addOrUpdateProductManual(found.product, qty);

                    } else {
                        showError("Insufficient uncommitted quantity for barcode : " + barcode);
                    }
                } else {
                    showError("Barcode not found in Bin inventories.");
                }
            } else {
                showError("No Bin inventories available.");
            }

            $('#loading').hide();
            $('#product_barcode_scan').attr('disabled', false);
            $('#product_barcode_scan').focus();

        }

        function onProductScanSuccessManual(barcode, qty) {

            $('#product_barcode_scan').attr('disabled', true);

            if (bin_inventories && bin_inventories.length > 0) {
                // Search for the barcode and check uncommitted_qty
                var found = bin_inventories.find(function(inventory) {
                    return inventory.barcode === barcode;
                });

                if (found) {
                    if (qty <= found.uncommitted_qty) {
                        // Deduct the qty
                        found.uncommitted_qty -= qty;
                        $('#products_grid_div').show();
                        $('#btn_send_for_picking').show();
                        addOrUpdateProductManual(found.product, qty);

                    } else {
                        showError("Insufficient uncommitted quantity for barcode : " + barcode);
                    }
                } else {
                    showError("Barcode not found in Bin inventories.");
                }
            } else {
                showError("No Bin inventories available.");
            }

            $('#loading').hide();
            $('#product_barcode_scan').attr('disabled', false);
            $('#product_barcode_scan').focus();

        }

        function onProductScanPutawaySuccess(qrCodeMessage) {

            var barcode = qrCodeMessage;
            var qty = 1;

            if (trolley_contents_for_putaway && trolley_contents_for_putaway.length > 0) {
                // Search for the barcode and check uncommitted_qty
                var found = trolley_contents_for_putaway.find(function(item) {
                    return item.product.barcode === barcode;
                });

                if (found) {
                    if (qty <= (found.qty_picked - found.qty_putaway) ) {
                        // Deduct the qty
                        
                        found.product.qty_picked = found.qty_picked;
                        found.product.qty_putaway = found.qty_putaway;
                        found.product.product_id = found.product.id;
                        $('#products_grid_div_putaway').show();
                        $('#btn_send_for_picking_putaway').show();
                        addOrUpdateProductInPutaway(found.product, 1);
                        
                        found.qty_picked -= qty;
                        product_data_received = 1;

                    } else {
                        showError("Insufficient picked quantity for barcode : " + barcode);
                    }
                } else {
                    showError("Barcode not found in Picked inventories.");
                }
            } else {
                showError("Please picked items in Trolley first.");
            }
            $('#loading').hide();
        }


        function onProductScanPutawaySuccessManual(barcode, qty) {

            $('#product_barcode_scan_putaway').attr('disabled', true);

            if (trolley_contents_for_putaway && trolley_contents_for_putaway.length > 0) {
                // Search for the barcode and check uncommitted_qty
                var found = trolley_contents_for_putaway.find(function(item) {
                    return item.product.barcode === barcode;
                });

                if (found) {
                    if (qty <= found.qty_picked) {
                        // Deduct the qty
                        found.product.qty_picked = found.qty_picked;
                        found.product.qty_putaway = found.qty_putaway;
                        found.product.product_id = found.product.id;
                        $('#products_grid_div_putaway').show();
                        $('#btn_send_for_picking_putaway').show();
                        addOrUpdateProductInPutaway(found.product, parseInt(qty));
                        
                        found.qty_picked -= qty;
                        product_data_received = 1;

                    } else {
                        showError("Insufficient picked quantity for barcode : " + barcode);
                    }
                } else {
                    showError("Barcode not found in Picked inventories.");
                }
            } else {
                showError("Please picked items in Trolley first.");
            }

            $('#loading').hide();
            $('#product_barcode_scan_putaway').attr('disabled', false);
            $('#product_barcode_scan_putaway').focus();

        }

        function getProductQtyIfExist(barcode) {
            var products = picking_data.products;
            for (var i = 0; i < products.length; i++) {
                if (products[i].barcode === barcode) {
                    return products[i].quantity + 1; // Exit the loop with the incremental qty
                }
            }
            return 1; // default qty
        }

        function getProductQtyIfExistManual(barcode, qty) {
            var products = picking_data.products;
            for (var i = 0; i < products.length; i++) {
                if (products[i].barcode === barcode) {
                    return parseInt(products[i].quantity) + parseInt(qty); // Exit the loop with the incremental qty
                }
            }
            return parseInt(qty); // default qty
        }

        function addOrUpdateProduct(productObj, quantityToAdd) {
            var productId = productObj.id;
            var products = picking_data.products;
            for (var i = 0; i < products.length; i++) {
                if (products[i].id === productId) {
                    // Increment quantity
                    products[i].quantity += quantityToAdd;
                    $('#product_row_' + productId).html(parseInt(products[i].quantity));
                    return; // Exit the loop once the product is found and updated
                }
            }
            productObj.quantity = 1; // default qty
            picking_data.products.push(productObj);
            $('#products_grid tr:last').after('<tr class="blink" id="product_' + productObj.id + '"><td>' + productObj.SKU +
                '</td><td>' + productObj.barcode + "</td><td id='product_row_" + productObj.id + "'>1</td></tr><");

        }

        function addOrUpdateProductManual(productObj, quantityToAdd) {
            quantityToAdd = parseInt(quantityToAdd);
            var productId = productObj.id;
            var products = picking_data.products;
            for (var i = 0; i < products.length; i++) {
                if (products[i].id === productId) {
                    // Increment quantity
                    products[i].quantity += parseInt(quantityToAdd);
                    $('#product_row_' + productId).html(parseInt(products[i].quantity));
                    return; // Exit the loop once the product is found and updated
                }
            }
            productObj.quantity = quantityToAdd; // default qty
            picking_data.products.push(productObj);
            $('#products_grid tr:last').after('<tr class="blink" id="product_' + productObj.id + '"><td>' + productObj.SKU +
                '</td><td>' + productObj.barcode + "</td><td id='product_row_" + productObj.id + "'>" + quantityToAdd +
                "</td></tr><");

        }

        function getPutawayProductQtyIfExist(barcode) {
            var products = putaway_data.products;
            for (var i = 0; i < products.length; i++) {
                if (products[i].barcode === barcode) {
                    return products[i].quantity + 1; // Exit the loop with the incremental qty
                }
            }
            return 1; // default qty
        }

        function addOrUpdateProductInPutaway(productObj, quantityToAdd) {
            var productId = productObj.product_id;
            var products = putaway_data.products;
            for (var i = 0; i < products.length; i++) {
                if (products[i].product_id === productId) {
                    // Increment quantity
                    products[i].quantity += quantityToAdd;
                    $('#product_row_' + productId).html(parseInt(products[i].quantity));
                    return; // Exit the loop once the product is found and updated
                }
            }
            productObj.quantity = quantityToAdd; // default qty
            putaway_data.products.push(productObj);
            $('#products_grid_putaway tr:last').after('<tr class="blink" id="product_' + productObj.product_id + '"><td>' +
                productObj.SKU + '</td><td>' + productObj.barcode + "</td><td>" + (productObj.qty_picked - productObj
                    .qty_putaway) + " /  <span id='product_row_" + productObj.product_id + "'>"+quantityToAdd+" </span></td></tr><");

        }

        function onLocationScanSuccess(qrCodeMessage) {
            $(document).ready(function() {
                var Data = new Object();
                Data.location_barcode = qrCodeMessage;
                Data.seller_id = 1;
                $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/get-location-details-from-barcode/',
                    data: Data,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                        if (response.success == true) {
                            picking_data.location_id = response.data.seller_ffc_location.id;
                            bin_inventories = response.data.bin_ffc_inventories;
                            $('#location_scan_field').hide();
                            $('#product_scan_field').show();
                            setTimeout(function() {
                                $('#product_barcode_scan').focus()
                            }, 1000);
                            $('#location_card').show();
                            $('#location_name').html(response.data.seller_ffc_location.location_name);
                            // $('#location_capacity').html("Capacity : " + response.data.available_capacity);
                        } else {
                            showError("Barcode Invalid");
                        }
                    },

                });
            });

        }

        function onLocationPutawayScanSuccess(qrCodeMessage) {
            $(document).ready(function() {
                var Data = new Object();
                Data.location_barcode = qrCodeMessage;
                Data.seller_id = 1;
                $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/get-location-details-from-barcode/',
                    data: Data,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                        if (response.success == true) {
                            putaway_data.location_id = response.data.seller_ffc_location.id;
                            bin_inventories = response.data.bin_ffc_inventories;
                            $('#location_scan_field_putaway').hide();
                            $('#product_scan_field_putaway').show();
                            setTimeout(function() {
                                $('#product_barcode_scan_putaway').focus()
                            }, 1000);
                            $('#location_card_putaway').show();
                            $('#location_name_putaway').html(response.data.seller_ffc_location.location_name);
                            // $('#location_capacity_putaway').html("Capacity : " + response.data.available_capacity);
                        } else {
                            showError("Barcode Invalid");
                        }
                    },

                });
            });

        }


        $('#location_barcode_scan').keypress(function(event) {
            if (event.keyCode == 13) {
                if ($('#location_barcode_scan').val().trim() == "") {
                    $('#location_barcode_scan').val("");
                    return;
                }
                $('#loading').show();
                onLocationScanSuccess($('#location_barcode_scan').val());
                $('#location_barcode_scan').val("");
            }
        });
        $('#product_barcode_scan').keypress(function(event) {
            if (event.keyCode == 13) {
                if ($('#product_barcode_scan').val().trim() == "") {
                    $('#product_barcode_scan').val("");
                    return;
                }
                if ( !{{$item_wise_stock_movement_setting}} ) {
                    $('#barcodeScannedManualQTY').html($('#product_barcode_scan').val());
                    $('#ModalManualQTY').modal('show');
                    setTimeout(function() {
                        $('#barcodeManualQTY').focus()
                    }, 1000);
                } else {
                    $('#loading').show();
                    onProductScanSuccess($('#product_barcode_scan').val());
                    $('#product_barcode_scan').val("");
                }

            }
        });

        function barcodeManualQtyProcess() {
            $('#loading').show();
            onProductScanSuccessManual($('#product_barcode_scan').val(), $('#barcodeManualQTY').val());
            $('#product_barcode_scan').val("");
            $('#barcodeScannedManualQTY').html("");
            $('#barcodeManualQTY').val("");
            $('#ModalManualQTY').modal('hide');
        }


        $('#barcodeManualQTY').keypress(function(event) {
            if (event.keyCode == 13) {
                event.preventDefault();
                barcodeManualQtyProcess();
            }
        });


        $('#location_barcode_scan_putaway').keypress(function(event) {
            if (event.keyCode == 13) {
                if ($('#location_barcode_scan_putaway').val().trim() == "") {
                    $('#location_barcode_scan_putaway').val("");
                    return;
                }
                $('#loading').show();
                onLocationPutawayScanSuccess($('#location_barcode_scan_putaway').val());
                $('#location_barcode_scan_putaway').val("");
            }
        });

        $('#product_barcode_scan_putaway').keypress(function(event) {
            if (event.keyCode == 13) {
                if ($('#product_barcode_scan_putaway').val().trim() == "") {
                    $('#product_barcode_scan_putaway').val("");
                    return;
                }
                if ( !{{$item_wise_stock_movement_setting}} ) {
                    $('#barcodeScannedManualQTYPutaway').html($('#product_barcode_scan_putaway').val());
                    $('#ModalManualQTYPutaway').modal('show');
                    setTimeout(function() {
                        $('#barcodeManualQTYPutaway').focus()
                    }, 1000);
                } else {
                    $('#loading').show();
                    onProductScanPutawaySuccess($('#product_barcode_scan_putaway').val());
                    $('#product_barcode_scan_putaway').val("");
                }
            }
        });


        function barcodeManualQtyProcessPutaway() {
            $('#loading').show();
            onProductScanPutawaySuccessManual($('#product_barcode_scan_putaway').val(), $('#barcodeManualQTYPutaway').val());
            $('#product_barcode_scan_putaway').val("");
            $('#barcodeScannedManualQTYPutaway').html("");
            $('#barcodeManualQTYPutaway').val("");
            $('#ModalManualQTYPutaway').modal('hide');
        }


        $('#barcodeManualQTYPutaway').keypress(function(event) {
            if (event.keyCode == 13) {
                event.preventDefault();
                barcodeManualQtyProcessPutaway();
            }
        });

        function showError(error) {
            $("#error_text").text(error);
            $("#error_message_toast").addClass("show");
        }

        var picking_submitted = false;

        function savePicking() {
            if (picking_submitted == false) {
                picking_submitted = true;
                $('#loading').show();
                $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/create-stock-movement/',
                    data: picking_data,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                        if (response.success == true) {
                            if (response.data.failed_check.length > 0) {
                                $('#js_success_message').html("Following Item(s) (" + response.data.failed_check
                                    .length + ") doesn't have enough inventory or are in Picking");
                                response.data.failed_check.forEach(function(item) {
                                    $('#js_success_message').append("<br/>" + item);
                                });
                                $('#successIcon').hide();
                                $('#warningIcon').show();
                                $('#jsSuccess').show();
                            } else {
                                $('#js_success_message').html(response.message);
                                $('#jsSuccess').show();
                            }
                        } else {
                            showError("Error Picking Items");
                            picking_submitted = false;
                        }
                    },

                });
            }
        }
        var putaway_submitted = false;

        function savePutaway() {
            if (putaway_submitted == false) {
                putaway_submitted = true;
                $('#loading').show();
                $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/move-stock/',
                    data: putaway_data,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                        if (response.success == true) {
                            var allOk = true;

                            if (allOk) {
                                $('#js_success_message').html(response.message);
                                $('#jsSuccess').show();

                            }
                        } else {
                            showError("Error Picking Items");
                            putaway_submitted = false;
                        }
                    },

                });
            }
        }


        // $(window).bind('beforeunload', function(){
        //              return 'Are you sure you want to leave?';
        //         });
    </script>


@endsection
<b></b>
