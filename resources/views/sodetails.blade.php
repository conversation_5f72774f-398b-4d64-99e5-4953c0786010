@extends('layout.innerlayout')
@section('content')

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

    <style>
        .floating-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background-color: #6457A6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        .floating-btn ion-icon {
            font-size: 24px;
        }
    </style>

    <!-- App Capsule -->
    <!-- App Capsule -->
    <div id="appCapsule">


        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>



        <div class="modal fade modalbox" id="ModalLocation" data-bs-backdrop="static" tabindex="-1" role="dialog">

            <div id="ModalLocationLoading" class="toast-box toast-center" style="z-index: 9999">
                <div class="in">
                    <div class="spinner-border text-success" role="status">
                    </div>
                    <div style="margin-top: 5px" class="text">
                        Loading.....
                    </div>
                </div>
            </div>


            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enter Putaway Location</h5>
                        <a href="#" data-bs-dismiss="modal">Close</a>
                    </div>
                    <div class="modal-body">
                        <div class="section mt-4 mb-5">
                            <div class="form-group basic">

                                <div class="input-wrapper" style="margin-bottom: 20px">
                                    <input type="text" name="location_barcode_scan" placeholder="Scan Location Barcode" id="location_barcode_scan" style="width: 100%;" value="" />
                                </div>
                            </div>

                            <div class="mt-2">
                                <button type="button" onclick="checkLocation()" class="btn btn-primary btn-block btn-lg">Submit</button>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- toast top iconed -->
        <!-- TODO: Back button on navigation still shows the loader on the previous page -->
        @if (session()->has('message'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                        {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        @if (session()->has('errors'))
            <div id="toast-3" class="toast-box toast-center show">
                @foreach ($errors->all() as $error)
                    <div class="in">

                        <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                        <div class="text">
                            {{ $error }}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                @endforeach
            </div>
        @endif
        @if (Session::has('error'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                    <div class="text">
                        {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        <!-- * toast top iconed -->
        <div class="section full mt-1 mb-2">
            <div class="section-title">SO Detail</div>


            <div class="wide-block p-0">

                <div class="table-responsive">
                    <form action="/update-so-details" id="update-so-details-form" method="POST">
                        {{ csrf_field() }}

                        <table class="table">
                            <thead>
                                <tr>
                                    <th scope="col" style="width: 50%;">SKU</th>
                                    <th scope="col" style="width: 10%;">Expected QTY</th>
                                    <th scope="col" style="width: 10%;">Remaining QTY</th>
                                    <th scope="col">QTY</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($stock_order_items as $so)
                                    <tr>
                                        <td style="overflow-wrap: anywhere;">{{ $so->sku }}</td>
                                        <td>{{ $so->qty }}</td>
                                        @if ($so->qty_received <= $so->qty)
                                            <td>{{ $so->qty - $so->qty_received }}</td>
                                        @else
                                            <td>0</td>
                                        @endif
                                        @if ($over_delivery_settings == 1)
                                            <td><input style="width:40px" type="number" onchange="soItemUpdate({{ $so->id }}, this.value)" min="0" />
                                        @else
                                            <td><input style="width:40px" type="number" onchange="soItemUpdate({{ $so->id }}, this.value)" min="0" max="{{ $so->qty - $so->qty_received }}" />
                                        @endif
                                        <!-- TODO: Highlight the row that has changed, on huge number of items it's not visible -->
                                        </td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                        <div class="card-body" id="save_button_div">
                            <button type="submit" disabled="true" class="btn btn-primary btn-block btn-lg" id="save_button_id" onclick="javascript:$('#save_button_div').hide();$('#save-items-receive-loader').show(); $('#loading').show();">Receive Only</button>
                            <hr>
                            <button type="button" disabled="true" id="save_button_id2" onclick="receivedAndPutaway()" class="btn btn-primary btn-block btn-lg">Receive and Putaway</button>
                            <div style="text-align: center;">
                                <div class="spinner-border text-primary" id="save-items-receive-loader" role="status" style="display:none;"></div>
                            </div>
                        </div>
                        <input type="hidden" name="putaway_location" id="putaway_location" />
                        <input type="hidden" name="order_items_scanned" id="order_items_scanned" />
                        <input type="hidden" name="stock_order_id" value="{{ $so_id }}" />
                    </form>
                </div>

            </div>
        </div>




    </div>
    <!-- * App Capsule -->
    <!-- * App Capsule -->


    <!-- Scroll to Bottom Button -->
        <div id="scrollToBottomBtn" class="floating-btn" style="display: none;">
            <ion-icon name="arrow-down-outline"></ion-icon>
        </div>
    <!-- Scroll to Bottom Button -->

    <script>
        $(window).bind('beforeunload', function() {
            return 'Are you sure you want to leave?';
        });



        // Scroll to bottom when button is clicked
        $(document).ready(function() {

            $('#scrollToBottomBtn').on('click', function() {
                $('html, body').animate({
                    scrollTop: $(document).height()
                }, 800);
            });

            // Check on page load
            checkContentHeight();

            // Check on window resize
            $(window).resize(function() {
                checkContentHeight();
            });

            // Check on scroll
            $(window).scroll(function() {
                checkContentHeight();
            });
        });


        var form_submitted = false;
        var so_items_scanned = {};




        function receivedAndPutaway() {
            $('#ModalLocation').modal('show');
        }

        $('#location_barcode_scan').keyup(e => {
            if (e.keyCode == 13) {
                checkLocation();
            }
        });


        function checkLocation() {
            let location_barcode = $('#location_barcode_scan').val();

            if (location_barcode) {

                if (!form_submitted) {

                    var Data = new Object();
                    Data.location_barcode = location_barcode;
                    Data.seller_id = 1;

                    $('#ModalLocationLoading').show();

                    $.ajax({
                        url: '{{ env('UNITY_URL') }}/api/ffc/get-location-details-from-barcode/',
                        data: Data,
                        crossDomain: true,
                        dataType: 'json',
                        type: 'post',
                        headers: {
                            "Authorization": "Bearer " + "{{ session('token') }}",
                        },
                        success: function(response) {

                            if (response.success == true) {

                                if (!form_submitted) {

                                    form_submitted = true;
                                    $('#loading').show();
                                    $('#putaway_location').val(response.data.seller_ffc_location.id);
                                    $('#ModalLocation').modal('hide');
                                    $('#update-so-details-form').submit();
                                }

                            } else {
                                $('#ModalLocationLoading').hide();
                                alert("Barcode Invalid");
                            }
                        },

                    });
                }

            } else {
                alert("Enter location barcode first");
            }
        }

        function soItemUpdate(so_item_id, qty) {
            so_items_scanned['qty-'+so_item_id] = qty;
            $('#order_items_scanned').val(JSON.stringify(so_items_scanned));
            $('#save_button_id').attr('disabled', false);
            $('#save_button_id2').attr('disabled', false);
        }

        // Function to check if content is taller than viewport
        function checkContentHeight() {
            const documentHeight = $(document).height();
            const windowHeight = $(window).height();
            const scrollBtn = $('#scrollToBottomBtn');

            if (documentHeight > windowHeight + 100) { // Adding buffer of 100px
                scrollBtn.show();
            } else {
                scrollBtn.hide();
            }

            // Also hide button if already at the bottom
            if ($(window).scrollTop() + windowHeight >= documentHeight - 50) {
                scrollBtn.hide();
            }
        }
    </script>
@endsection
<b></b>
