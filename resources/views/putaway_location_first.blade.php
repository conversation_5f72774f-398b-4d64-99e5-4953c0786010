@extends('layout.mainlayout')
@section('content')
<style>
.blink {
  animation: blinker 0.5s ;
}

@keyframes blinker {
    from { background-color: #6C7C94; }
    to { background-color: inherit; }

}

.error {
    background-color: red;
    color: #fff;
}

</style>
    <!-- App Capsule -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="fab-button text bottom-center" style="margin-bottom:80px; display:none;" id="btn_send_to_putaway">
            <a href="#" class="fab" onclick="savePutaway()">
                <ion-icon name="add-outline" role="img" class="md hydrated" aria-label="add outline"></ion-icon>
                Putaway
            </a>
        </div>
        <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

        <div id="jsSuccess" class="toast-box toast-center">
            <div class="in">

                <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                <div class="text">
                    <span id="js_success_message"></span>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button" onclick="location.href='/putaway_location_first'">CLOSE</button>
        </div>

        <div id="jsError" class="toast-box toast-center">
            <div class="in">

                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text">
                   <span id="js_error_message"></span>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-text-success close-button" onclick="javascript:$('#jsError').hide();">CLOSE</button>
        </div>


        @if (session()->has('message'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                        {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif
        @if (session()->has('errors'))
            <div id="toast-3" class="toast-box toast-top show">
                @foreach ($errors->all() as $error)
                    <div class="in">

                        <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                        <div class="text">
                            {{ $error }}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
                @endforeach
            </div>
        @endif
        @if (Session::has('error'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">

                    <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                    <div class="text">
                        {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>
        @endif

        <div class="section full mt-1 mb-2">

            <div class="section full mt-2" id="location_scan_field">
                <div class="invoice" style="">
                        <input type="text" name="location_barcode_scan" placeholder="Scan Location Barcode"
                        id="location_barcode_scan" autofocus style="width: 100%;" value="" />
                </div>
            </div>
            <div class="card text-white bg-success mb-2" id="location_card" style="display: none; text-align:center; margin-left:10px;margin-right:10px;">
                <div class="card-header"><span id="location_name"></span></div>
                    <p class="card-header"><span id="location_capacity"></span></p>
            </div>
            <div class="section full mt-2" id="product_scan_field" style="display:none;">
                <div class="invoice" style="">
                    <input type="text" name="product_barcode_scan" placeholder="Scan Product Barcode"id="product_barcode_scan" autofocus style="width: 100%;" value="" />
                </div>
            </div>
                <div class="wide-block p-0">

                    <div class="table-responsive">
                        <form action="/update-so-details" method="POST">
                            {{ csrf_field() }}

                            <span class="p-2" id="total_count"></span>

                            <table class="table" id="products_grid">
                                <thead>
                                    <tr>
                                        <th scope="col">SKU</th>
                                        <th scope="col">Barcode</th>
                                        <th scope="col">QTY</th>
                                    </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                            <div class="card-body">
                            </div>
                            <input type="hidden" name="stock_order_id" value="" />
                        </form>
                    </div>

                </div>

        </div>

        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>


        <div class="toast-box toast-center" id="error_message_toast" style="z-index: 9999">
            <div class="in">
                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text" id="error_text">
                </div>
            </div>
        </div>


    </div>
    <!-- * App Capsule -->
    <!-- * App Capsule -->

    <script>
        var session = "{{ session('token') }}";
        var putAwayData = new Object();
        putAwayData.products = [];
        let total_items = 0;

        function onLocationScanSuccess(qrCodeMessage) {
            $(document).ready(function() {
                var Data = new Object();
                Data.location_barcode = qrCodeMessage;
                Data.seller_id = 1;
                $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/get-location-details-from-barcode/',
                    data: Data,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                        //console.log(response);
                        if (response.success == true) {
                            putAwayData.location_id=response.data.seller_ffc_location.id;
                            $('#location_scan_field').hide();
                            $('#product_scan_field').show();
                            setTimeout(function() {  $('#product_barcode_scan').focus()}, 1000);
                            $('#location_card').show();
                            $('#location_name').html(response.data.seller_ffc_location.location_name);
                            $('#location_capacity').html("Capacity : " + response.data.seller_ffc_location.available_capacity);
                        } else {
                            showError("Barcode Invalid");
                        }
                    },

                });
            });

        }
        function onProductScanSuccess(qrCodeMessage) {
            $(document).ready(function() {
                var Data = new Object();
                Data.product_barcode = qrCodeMessage;
                Data.seller_id = 1;
                $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/get-pending-putaway-item-from-barcode/',
                    data: Data,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                       console.log(response);
                        if (response.success == true) {
                            if(response.data[Object.keys(response.data)[0]].pending_putaway <= parseInt($('#product_row_'+response.data[Object.keys(response.data)[0]].product_id).html()))
                            {
                                showError("Putaway cannot be greater than " + response.data[Object.keys(response.data)[0]].pending_putaway);
                                return;
                            }

                            if($('#product_row_'+response.data[Object.keys(response.data)[0]].product_id).length){
                                $('#product_row_'+response.data[Object.keys(response.data)[0]].product_id).html(parseInt($('#product_row_'+response.data[Object.keys(response.data)[0]].product_id).html())+1);
                                $('#product_row_'+response.data[Object.keys(response.data)[0]].product_id).addClass('blink');
                            }
                            else
                            {
                                total_items++;
                                $('#products_grid tr:last').after('<tr class="blink" id="product_'+response.data[Object.keys(response.data)[0]].product_id+'"><td>'+Object.keys(response.data)[0]+'</td><td>'+response.data[Object.keys(response.data)[0]].barcode+"</td><td id='product_row_" + response.data[Object.keys(response.data)[0]].product_id + "'>1</td></tr><");
                            }
                            putAwayData.products.push(response.data[Object.keys(response.data)[0]].product_id);
                            $('#total_count').html(`Total SKU(s) : <b>${total_items}</b> &nbsp;&nbsp;&nbsp;&nbsp; Total Item(s) Qty : <b>${putAwayData.products.length}</b>`);
                            $('#btn_send_to_putaway').show();

                        } else {
                            showError(response.message);
                        }
                    },

                });
            });

        }
        var putaway_submitted = false;
        function savePutaway()
        {
            if(putaway_submitted == false){
                putaway_submitted = true;
            $('#loading').show();
            $.ajax({
                    url: '{{ env('UNITY_URL') }}/api/ffc/save-location-first-putaway/',
                    data: putAwayData,
                    crossDomain: true,
                    dataType: 'json',
                    type: 'post',
                    headers: {
                        "Authorization": "Bearer " + session,
                    },
                    success: function(response) {
                        $('#loading').hide();
                        console.log(response);
                        if (response.success == true) {
                            var allOk=true;
                            $.each(response.data, function (key,val)
                            {
                                if(!response.data[key].original.success)
                                {
                                    $('#product_'+key).addClass('error');
                                    $('#js_error_message').html(response.data[key].original.data.error);
                                    $('#jsError').show();
                                    allOk=false;
                                }
                                else
                                {
                                    $('#product_'+key).remove();
                                    console.log(key);
                                    putAwayData.products =  putAwayData.products.filter((el) => el!=key);
                                }
                            });
                            if(allOk){
                            $('#js_success_message').html(response.message);
                            $('#jsSuccess').show();
                            }
                        } else {
                            showError("Error Saving Items");
                        }
                        putaway_submitted = false;
                    },

                });
            }
            //console.log(putAwayData);
        }

        $('#location_barcode_scan').keypress(function(event) {
            if (event.keyCode == 13) {
                if($('#location_barcode_scan').val().trim()==""){
                $('#location_barcode_scan').val("");
                return;
            }
                $('#loading').show();
                onLocationScanSuccess($('#location_barcode_scan').val());
                $('#location_barcode_scan').val("");
            }
        });
        $('#product_barcode_scan').keypress(function(event) {
            if (event.keyCode == 13) {
                if($('#product_barcode_scan').val().trim()==""){
                $('#product_barcode_scan').val("");
                return;
            }
                $('#loading').show();
                onProductScanSuccess($('#product_barcode_scan').val());
                $('#product_barcode_scan').val("");
            }
        });

        function showError(error) {
            $("#error_text").text(error);
            toastbox('error_message_toast', 2000);
        }

        function removeItem(arr,removeItem) {
            $.each(arr, function(key, value){
                if(value==removeItem)
                {

                }
            });
        }

    </script>


@endsection
<b></b>
