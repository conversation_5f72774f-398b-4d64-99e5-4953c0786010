@extends('layout.mainlayout')
@section('content')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

   <!-- App Capsule -->
    <!-- App Capsule -->
    <div id="appCapsule">
          <!-- toast top iconed -->

          @if(session()->has('message'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
            @if(session()->has('errors'))
            @foreach($errors->all() as $error)
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">

                    <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                    <div class="text">
                    {{ $error }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

                @endforeach
             </div>

            @endif
            @if(Session::has('error'))
            <div id="toast-3" class="toast-box toast-center show">
                <div class="in">

                    <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                    <div class="text">
                    {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif

        <!-- * toast top iconed -->

        <div class="section full mt-1 mb-2">
        <div class="section full mt-2" >
            <div class="invoice" style="">
                <div class="invoiceBackgroundLogo">
                    <img src="" alt="">
                </div>



                <!-- TODO: this field is accepting id with spaces too and moving forward  -->
                <input type="text" name="barcode_scan" placeholder="Scan SO ID"  id="barcode_scan" autofocus style="width: 100%;" value=""/>

            </div>
        </div>
            <div class="wide-block p-0">

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">SO ID</th>
                                <th scope="col">Arrival Date</th>
                                <th scope="col">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(count($stock_orders) > 0)
                            @php
                               $count = 1;
                            @endphp
                            @foreach($stock_orders as $so)

                            <tr>
                                <th scope="row">{{$count}}</th>
                                @if($so->status != "Received")
                                <td><a href="sodetails/{{$so->id }}" onclick="$('#loading').show();"> {{$so->reference_id }}</a></td>
                                <!-- TODO: Loader on clicking item in the grid -->
                                @else
                                <td> {{$so->reference_id }}</td>
                                @endif
                                <td>{{$so->arrival_date}}</td>
                                <td>{{$so->status}}</td>
                            </tr>
                            @php
                                $count++;
                            @endphp
                            @endforeach
                            @else

                            <tr>
                            <td></td>
                            <td></td>
                            <td> No Data Found</td>
                            <td> </td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                </div>

            </div>
        </div>

        <div id="loading" class="toast-box toast-center" style="z-index: 9999">
            <div class="in">
                <div class="spinner-border text-success" role="status">
                </div>
                <div style="margin-top: 5px" class="text">
                    Loading.....
                </div>
            </div>
        </div>


        <div class="toast-box toast-center" id="error_message_toast" style="z-index: 9999">
            <div class="in">
                <ion-icon name="warning-sharp" style="color:red;"></ion-icon>
                <div class="text" id="error_text">
                </div>
            </div>
         </div>


    </div>
    <!-- * App Capsule -->
<!-- * App Capsule -->
<script>
  $(document).ready(function() {

var scanned_barcode_arr = [];
    let barcodes_array = @json($barcodes_arr);
    console.log(barcodes_array);
    $('#barcode_scan').keyup(function(e){
        if(e.keyCode == 13)
        {
            var item_barcode = $(this).val();
            item_barcode = item_barcode.toLowerCase().trim();
           //console.log(item_barcode);

            if(item_barcode in barcodes_array){
                $('#loading').show();
                window.location.href = "/sodetails/"+barcodes_array[item_barcode]['so_id'];

            } else{
                showError('SO ID is invalid');
            }
            $(this).val("");

        }

    })

});


function showError(error) {
    $("#error_text").text(error);
    toastbox('error_message_toast', 2000);
}
</script>
@endsection
<b></b>
