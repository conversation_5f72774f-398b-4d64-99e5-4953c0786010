 <!-- App Bottom Menu -->
 <div class="appBottomMenu">
        <a href="/home" class="item {{$data['home_active']}}" onclick="javascript:$('#loading').show();">
            <div class="col">
                <ion-icon name="home-outline"></ion-icon>
            </div>
        </a>
        <a href="/security" class="item {{$data['security_active']}}" onclick="javascript:$('#loading').show();">
            <div class="col">
            <i class="bi bi-arrow-bar-right"></i>
            </div>
        </a>
        <!--<a href="/pending_putaway" class="item {{$data['putaway_active']}}" onclick="javascript:$('#loading').show();">-->
        <a data-bs-toggle="offcanvas" class="item {{$data['putaway_active']}}" href="#offcanvas-bottom">
            <div class="col">
            <i class="bi bi-arrow-bar-up"></i>
            </div>
        </a>
        <a href="/pick" class="item {{$data['pick_active']}}" onclick="javascript:$('#loading').show();">
            <div class="col">
            <i class="bi bi-file-earmark-text"></i>
            </div>
        </a>

    </div>


   <!-- //TODO: Remove this once notification is removed globally -->
    <!-- danger style-->
    <div id="notification-error" class="notification-box">
        <div class="notification-dialog ios-style bg-danger">
            <div class="notification-header">
                <div class="in">
                    <strong> &nbsp; System</strong>
                </div>
                <div class="right">
                    <span>just now</span>
                    <a href="#" class="close-button">
                        <ion-icon name="close-circle"></ion-icon>
                    </a>
                </div>
            </div>
            <div class="notification-content">
                <div class="in">
                    <h3 class="subtitle">Error</h3>
                    <div class="text"> </div>
                </div>
            </div>
        </div>
    </div>
    <!-- * danger style -->






    <!-- success style-->
    <!--
    <div id="notification-success" class="notification-box">
        <div class="notification-dialog ios-style bg-success">
            <div class="notification-header">
                <div class="in">
                    <strong> &nbsp; System</strong>
                </div>
                <div class="right">
                    <span>just now</span>
                    <a href="#" class="close-button">
                        <ion-icon name="close-circle"></ion-icon>
                    </a>
                </div>
            </div>
            <div class="notification-content">
                <div class="in">
                    <h3 class="subtitle">Successfull</h3>
                    <div class="text"> </div>
                </div>
            </div>
        </div>
    </div> -->
    <div id="notification-success" class="toast-box toast-center">
        <div class="in">

            <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
            <div class="text"></div>
        </div>
    </div>
    <!-- * success style -->


    <!-- * App Bottom Menu -->
