
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

    <!-- App Sidebar -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="sidebarPanel">
        <div class="offcanvas-body">
            <!-- profile box -->
            <div class="profileBox" style="background-color: white">
                    <div style="margin:10px">
                    <img src="assets/img/flash.png" alt="image" class="imaged w-75" >
                    </div>

                <a href="#" class="close-sidebar-button" data-bs-dismiss="offcanvas">
                    <ion-icon name="close"></ion-icon>
                </a>
            </div>
            <!-- * profile box -->

            <ul class="listview flush transparent no-line image-listview mt-2">
                 <li>
                    <a href="/home" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <div class="icon-box bg-primary">
                        <ion-icon name="home-outline"></ion-icon>
                        </div>
                        <div class="in">
                            Home
                        </div>
                    </a>
                </li>
                <li>
                    <a href="/security" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <div class="icon-box bg-primary">
                        <i class="bi bi-arrow-bar-right"></i>
                        </div>
                        <div class="in">
                            Receiving
                        </div>
                    </a>
                </li>
                <li>
                    <!--<a href="/pending_putaway" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">-->
                        <a data-bs-toggle="offcanvas" class="item" href="#offcanvas-bottom">
                        <div class="icon-box bg-primary">
                        <i class="bi bi-arrow-bar-up"></i>
                        </div>
                        <div class="in">
                            Putaway
                        </div>
                    </a>
                </li>
                <li>
                    <a href="/pick" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <div class="icon-box bg-primary">
                        <i class="bi bi-file-earmark-text-fill"></i>
                        </div>
                        <div class="in">
                            <div>Pick</div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="/transfer-outbound" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <div class="icon-box bg-primary">
                        <i class="bi bi-arrow-bar-right"></i>
                        </div>
                        <div class="in">
                            <div>Transfer Outbound</div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="/stock-picking" class="item" onclick="javascript: freeze_click = true; $('#loading').show();">
                        <div class="icon-box bg-primary">
                        <i class="bi bi-arrow-left-right"></i>
                        </div>
                        <div class="in">
                            <div>Stock Movement </div>
                        </div>
                    </a>
                </li>

                @if (session('ffc_app_item_counting_mode'))
                    <li>
                        <div class="item">
                            <div class="icon-box bg-primary">
                                <ion-icon name="barcode-outline"></ion-icon>
                            </div>
                            <div class="in">
                                <div>Item Wise Scanning</div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input item_counting_mode_switch" type="checkbox" @if (session('ffc_app_item_counting_mode_value')) checked @endif id="item_counting_mode_switch">
                                    <label class="form-check-label" for="item_counting_mode_switch"></label>
                                </div>
                            </div>
                        </div>
                    </li>
                @endif


            </ul>


        </div>
        <!-- sidebar buttons -->
        <div class="sidebar-buttons">

            <a href="/logout" class="button" onclick="javascript: freeze_click = true; $('#loading').show();">
                <ion-icon name="log-out-outline">Logout</ion-icon>

            </a>
        </div>
        <!-- * sidebar buttons -->
    </div>
    <div class="offcanvas offcanvas-bottom" tabindex="-1" id="offcanvas-bottom">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">Which Method?</h5>
            <a href="#" class="offcanvas-close" data-bs-dismiss="offcanvas">
                <ion-icon name="close-outline"></ion-icon>
            </a>
        </div>
        <div class="offcanvas-body">
            <div>
                    <a href="/putaway_location_first" class="btn btn-primary btn-lg btn-block" onclick="javascript: freeze_click = true; $('#loading').show(); ">Location First</a>
                    <p>&nbsp;</p>
                    <a href="/pending_putaway" class="btn btn-primary btn-lg btn-block" onclick="javascript: freeze_click = true; $('#loading').show();">Product First</a>
            </div>
        </div>
    </div>
    <!-- * App Sidebar -->


    <script>

        freeze_click = false; // just modify that variable to disable all clics events

        document.addEventListener("click", e => {
            if (freeze_click) {
                e.stopPropagation();
                e.preventDefault();
            }
        }, true);


        $("#item_counting_mode_switch").change(function() {

            $.ajax({
                url: '/item-counting-switch-mode/'+(this.checked ? 1 : 0),
                success: function(response) {
                },

            });
        });

    </script>
