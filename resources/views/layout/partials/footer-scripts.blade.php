  <!-- ============== Js Files ==============  -->
  <!-- Bootstrap -->
  <script src="{{ asset('assets/js/lib/bootstrap.min.js') }}"></script>
  <!-- Ionicons -->
  <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
  <!-- Splide -->
  <script src="{{ asset('assets/js/plugins/splide/splide.min.js') }}"></script>
  <!-- ProgressBar js -->
  <script src="{{ asset('assets/js/plugins/progressbar-js/progressbar.min.js') }}"></script>
  <!-- Base Js File -->
  <script src="{{ asset('assets/js/base.js') }}"></script>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>


  <script>
      // Trigger welcome notification after 5 seconds
      setTimeout(() => {
          // notification('notification-welcome', 5000);
      }, 2000);




      @if (session('success'))
          $('#notification-success .text').text('{{ session('success') }}');
          toastbox('notification-success', 2000);
      @endif

  </script>
