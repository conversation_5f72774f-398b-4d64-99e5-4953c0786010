@extends('layout.mainlayout')
@section('content')
   <!-- App Capsule -->
   <div id="appCapsule">



<div class="section full mt-1 mb-2">
    <div class="section-title">Orders
        <a href="/assign-picklist/release" ><button type="button" class="btn btn-danger btn-sm me-1 mb-1">Release</button></a>
    </div>
    <div class="section mb-2">
    
                     <!-- toast top iconed -->
        
            @if(session()->has('message'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    
                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ session()->get('message') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
            @if(session()->has('errors'))
            @foreach($errors->all() as $error)
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    
                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ $error }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>

                @endforeach
             </div>

            @endif
            @if(Session::has('error'))
            <div id="toast-3" class="toast-box toast-top show">
                <div class="in">
                    
                    <ion-icon name="checkmark-circle" class="text-success"></ion-icon>
                    <div class="text">
                    {{ Session::get('error') }}
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-text-success close-button">CLOSE</button>
            </div>

            @endif
        <!-- * toast top iconed -->

    <div class="wide-block p-0">

        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">Order</th>
                        <th scope="col">SKU</th>
                        <th scope="col">Date</th>
                    </tr>
                </thead>
                <tbody>
                @if(count($order_list) > 0)
                @php
                    $count = 1;
                @endphp
                @foreach($order_list as $or)

                    <tr>
                        <th scope="row">{{$count}}</th>
                        <td><a href="pickitems/{{$or->order_id}}"> {{$or->order_ref_id}} </a></td>
                        <td>{{$or->items_count}}</td>
                        <td>{{$or->created_at}}</td>
                    </tr>
                @php
                    $count++;
                @endphp
                @endforeach
                @else

                    <tr>
                    <td></td>
                    <td></td>
                    <td> No Data Found</td>
                      <td> </td>
                    </tr>
                @endif
                   
                </tbody>
            </table>
        </div>

    </div>
</div>





</div>
<!-- * App Capsule -->


</div>
<!-- * App Capsule -->
@endsection
<b></b>