server {
  listen 80;
  index index.php index.htm index.html;
  error_log /var/log/nginx/error.log;
  access_log /var/log/nginx/access.log;
  server_name localhost;
  root /var/www/public;

  location / {
      add_header 'Access-Control-Allow-Origin' '*' always;
      try_files $uri $uri/ /index.php?$query_string;

  }

  location /index.php {
      add_header 'Access-Control-Allow-Origin' '*' always;
      try_files $uri = 404;
      fastcgi_split_path_info ^(.+\.php)(/.+)$;
      fastcgi_pass php81ffc:9000;
      fastcgi_index index.php;
      include fastcgi_params;
      fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
      fastcgi_param PATH_INFO $fastcgi_path_info;


  }
}
