<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('login');
});

Route::get('/login', function () {
    return view('login');
 });



 Route::get('/logout', 'App\Http\Controllers\MainController@logout');
 Route::get('/home', 'App\Http\Controllers\MainController@home')->middleware('authselleruser');

 Route::get('/putaway', 'App\Http\Controllers\MainController@putAway')->middleware('authselleruser');
 Route::get('/pending_putaway', 'App\Http\Controllers\MainController@pendingPutAway')->middleware('authselleruser');
 Route::get('/putaway_new/{id}', 'App\Http\Controllers\MainController@putAwayNew')->middleware('authselleruser');


 Route::get('/putaway/suggest-location/{barcode}', 'App\Http\Controllers\MainController@suggestLocation')->middleware('authselleruser');
 Route::get('/pick', 'App\Http\Controllers\MainController@picking')->middleware('authselleruser');
 Route::post('/item-picked', 'App\Http\Controllers\MainController@itemPicked')->middleware('authselleruser');
 Route::get('/assign-picklist', 'App\Http\Controllers\MainController@assignPickList')->middleware('authselleruser');
 Route::get('/assign-picklist/release', 'App\Http\Controllers\MainController@releaseAssignedPickList')->middleware('authselleruser');
 Route::post('/assign-picklist/submit-to-pd', 'App\Http\Controllers\MainController@submitToPD')->middleware('authselleruser');
 Route::get('/security', 'App\Http\Controllers\MainController@securityScan')->middleware('authselleruser');
 Route::get('/sodetails/{id}', 'App\Http\Controllers\MainController@soDetails')->middleware('authselleruser');
 Route::get('/pickitems/{id}', 'App\Http\Controllers\MainController@pickItems')->middleware('authselleruser');
 Route::get('/transfer-outbound', 'App\Http\Controllers\MainController@transferOutbound')->middleware('authselleruser');
 Route::post('/transfer-outbound/pickitems/assign-location/completed', 'App\Http\Controllers\MainController@transferOutboundPickItemFfcLocationsCompleted')->middleware('authselleruser');
 Route::get('/transfer-outbound/pickitems/assign-location/{id}', 'App\Http\Controllers\MainController@transferOutboundPickItemFfcLocations')->middleware('authselleruser');
 Route::get('/transfer-outbound/pickitems/{id}/{stock_type?}', 'App\Http\Controllers\MainController@transferOutboundPickItems')->middleware('authselleruser');
 Route::post('/transfer-outbound/submit-to-pd', 'App\Http\Controllers\MainController@transferOutboundSubmitToPD')->middleware('authselleruser');
 Route::post('/transfer-outbound/item-picked', 'App\Http\Controllers\MainController@transferOutboundItemPicked')->middleware('authselleruser');
 Route::get('/stock-picking', 'App\Http\Controllers\MainController@stockPicking')->middleware('authselleruser');

 Route::post('/auth', 'App\Http\Controllers\MainController@login');

 Route::post('/update-so-details', 'App\Http\Controllers\MainController@updateSoDetails')->middleware('authselleruser');
 Route::post('/update-so-details-ajax', 'App\Http\Controllers\MainController@updateSoDetails_ajax')->name('update-so-details-name-ajax')->middleware('authselleruser');
 Route::post('/putaway_save', 'App\Http\Controllers\MainController@putAwaySave')->middleware('authselleruser');
 Route::post('/putaway_save_new', 'App\Http\Controllers\MainController@putAwaySaveNew')->middleware('authselleruser');


 Route::get('/reject-order-processing/{id}', 'App\Http\Controllers\MainController@rejectOrderProcessing')->middleware('authselleruser');
 Route::get('/accept-order-processing/{id}', 'App\Http\Controllers\MainController@acceptOrderProcessing')->middleware('authselleruser');

//  Route::get('/accept-transfer-order-processing/{id}', 'App\Http\Controllers\MainController@acceptTransferOrderProcessing')->middleware('authselleruser');
 Route::get('/putaway_location_first', 'App\Http\Controllers\MainController@putAwayLocationFirst')->middleware('authselleruser');
 Route::get('/item-counting-switch-mode/{value}', 'App\Http\Controllers\MainController@itemCountingModeSwitch')->middleware('authselleruser');