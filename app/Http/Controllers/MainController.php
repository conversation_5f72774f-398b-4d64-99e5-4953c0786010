<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class MainController extends Controller
{
    /**
     * Provision a new web server.
     *
     * @return \Illuminate\Http\Response
     */
    public function putAway()
    {
        $page_title = "Put Away";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "active";
        $data['pick_active'] = "";

        return view('putaway',compact('page_title','data'));
    }
    public function home()
    {
        $data['home_active'] = "active";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "";
        $page_title = "Home";
        return view('home',compact('page_title','data'));
    }
    public function picking()
    {
        $page_title = "Picking";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "active";


        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/get-ffc-orders-asigned-to-user',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
          ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);

        // dd($response->data);
        // return $response;
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){
                    $order_items = $response->data->data;
                    $current_pick_items = $response->data->current_pick_items;
                    $data['all_picked'] = $response->data->all_picked;
                    $data['picking_mode'] = $response->data->picking_mode;
                    $itemWisePickingSetting = $response->data->ItemwiseScanPicking;
                }elseif($response->success == false){
                    $order_items = [];
                    $current_pick_items = [];
                    if(isset($response->data->error) && $response->data->error == "Permission Denied"){
                        return redirect('/home')->with('error', $response->data->error);
                    } else {
                        return view('picking',compact('page_title','order_items','data'))->with('error', $response->message);
                    }
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }

        return view('pickitems',compact('page_title','order_items','current_pick_items','data', 'itemWisePickingSetting'));
    }



    public function itemPicked(Request $request)
    {
        $request->validate([
            'pick_item' => 'required',
        ]);

        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/item-picked',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('pick_item' => $request->pick_item),
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
          ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true) {
                    return redirect('/pick')->with('success', $response->message);
                } elseif($response->success == false) {
                    return redirect('/pick')->with('error', $response->message);
                }
            }
        }else{
            return redirect('/login');
        }

        return redirect('/pick')->with('error', 'Something went wrong');
    }

    public function securityScan()
    {
        $page_title = "Receiving";
        $data['home_active'] = "";
        $data['security_active'] = "active";
        $data['putaway_active'] = "";
        $data['pick_active'] = "";
        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/get-stock-order-request',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_HTTPHEADER => array(
          'Accept: application/json',
          'Authorization: Bearer '.session('token')
        ),
      ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }elseif($response->success == false){
                return redirect('/home')->with('error', $response->data->error);
            }else{
                $stock_orders = $response->data->stock_orders;
                $barcodes_arr = $response->data->barcodes_arr;

            }
        }else{
            return redirect('/login');
        }
        curl_close($curl);
        return view('security',compact('page_title','stock_orders','data','barcodes_arr'));
    }
    public function soDetails($id)
    {
        $page_title = "SO Details";
        $data['home_active'] = "";
        $data['security_active'] = "active";
        $data['putaway_active'] = "";
        $data['pick_active'] = "";
        $so_id = $id;

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/get-stock-order-request-items',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('stock_order_id' => $so_id),
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));
        $response = curl_exec($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            } elseif ($response->success == false) {
                return redirect('/security')->with('error', $response->data->error);
            } else{
                $stock_order_items = $response->data->stock_order_items;
                $over_delivery_settings = $response->data->over_delivery_settings;
                $item_wise_settings = $response->data->item_wise_settings;

                $barcodes_arr = $response->data->barcodes_arr;
                $barcodes_arr = json_decode(json_encode($barcodes_arr), true);

                $unique_id= uniqid() . '_' . time(); // to distinguish api request attempt of a warehouse user

                if($item_wise_settings == 1){
                    return view('sodetails_item_wise',compact('page_title','stock_order_items','so_id','data','over_delivery_settings','barcodes_arr','unique_id'));
                }

            }
        }else{
            return redirect('/login');
        }
        curl_close($curl);


        return view('sodetails',compact('page_title','stock_order_items','so_id','data','over_delivery_settings'));
    }
    public function pickItems($id)
    {
        $page_title = "Picking";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "active";
        $data['show_scanner_on_header'] = true;

        $so_id = $id;
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => env('UNITY_URL').'/api/ffc/get-ffc-items-inventory',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => array('order_id' => $so_id),
          CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){
                    $order_items = $response->data->data;
                    $order_data = $response->data->order_data;
                }elseif($response->success == false){
                    $order_items = [];
                    $order_data = [];
                    $order_id = 0;
                    return back()->with('error', $response->message);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }


        return view('pickitems',compact('page_title','order_items','order_data','data'));
    }
    public function login(Request $request){

        $request->validate([
        'email' => 'required',
        'password' => 'required',
    ]);
    $page_title = "Home";
    $data['home_active'] = "active";
    $data['security_active'] = "";
    $data['putaway_active'] = "";
    $data['pick_active'] = "";


        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/login',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('email' => $request->email,'password' => $request->password),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);
        if(isset($response->success) && $response->success == true){
            session(['token' => $response->data->token]);
            session(['seller_user_email' => $response->data->seller_user_email]);
            session(['putaway_location_suggestion' => $response->data->putaway_location_suggestion]);
            session(['ffc_app_item_counting_mode' => $response->data->ffc_app_item_counting_mode]);
            session(['ffc_app_item_counting_mode_value' => $response->data->ffc_app_item_counting_mode_value]);
            return view('home',compact('page_title','data'));
        }else{
            if(isset($response->success) && $response->success == false){
                return back()->with('error', $response->data->error);
            }else{
                return back()->with('error', "Something went wrong , Please reach out Admin for more details");
            }
        }


    }

    public function logout(){
        session()->forget('seller_id');
        return view('login');
    }

    public function updateSoDetails(Request $request){

        foreach(json_decode($request->order_items_scanned) as $key=>$value)
        {
            $data['products'][] = array(
                "id"=> substr($key, 4),
                "sku"=>"y",
                "qty"=>$value,
            );
        }

        $data['stock_order_id'] =$request->stock_order_id;
        $data['putaway_location'] = $request->putaway_location ?? null;

        $data2 = json_encode($data);


        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/update-stock-order-status',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => $data2,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){
                    if($response->message){
                        return redirect('/sodetails/'.$request->stock_order_id)->with('message', $response->message);
                    }else{
                        return redirect('/sodetails/'.$request->stock_order_id)->with('message', "Receiving Completed!");
                    }
                }elseif($response->success == false){
                    return back()->with('error', $response->data->error);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }
        curl_close($curl);
    }

    public function updateSoDetails_ajax(Request $request){

        foreach(json_decode($request->order_items_scanned) as $key=>$value)
        {
            $data['products'][] = array(
                "id"=> substr($key, 4),
                "sku"=>"y",
                "qty"=>$value,
            );
        }

        $data['stock_order_id'] =$request->stock_order_id;
        $data['putaway_location'] = $request->putaway_location ?? null;
        $data['unique_id'] = $request->unique_id ?? null;
        $data2 = json_encode($data);


        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/update-stock-order-status',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => $data2,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));

        $response = curl_exec($curl);
        \Log::info($response);

        $response = json_decode($response);
        $message_array = [];
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                $message_array['message_id'] = "Login_Required";
                $message_array['message'] = "Unauthenticated.";
            }else{
                if($response->success == true){
                    if($response->message){
                        $message_array['message_id'] = "Success";
                        $message_array['message'] = $response->message;
                    }else{
                        $message_array['message_id'] = "Success";
                        $message_array['message'] = "Receiving Completed!";
                    }
                }elseif($response->success == false){
                    $message_array['message_id'] = "Error";
                    $message_array['message'] = $response->data->error;
                }else{
                    $message_array['message_id'] = "Error";
                    $message_array['message'] = "Something went wrong";
                }
            }
        }else{
            $message_array['message_id'] = "Login_Required";
            $message_array['message'] = "Unauthenticated.";
        }
        curl_close($curl);
        return $message_array;
    }

    public function putAwaySave(Request $request){
        $request->validate([
            'product_barcode' => 'required',
            'location_barcode' => 'required',
            'qty' => 'required'
        ]);
        $product_barcode = $request->product_barcode;
        $location_barcode = $request->location_barcode;
        $qty = $request->qty;

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/update-putaway',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('product_barcode' =>  $product_barcode,'location_barcode' =>$location_barcode,'qty' => $qty),
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer '.session('token')
              ),
          ));

        $response = curl_exec($curl);

        curl_close($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){
                    return redirect('/putaway')->with('message', 'Put away submitted');
                }elseif(($response->success == false)){
                    return back()->with('error', $response->data->error);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }

    }

    public function rejectOrderProcessing($id)
    {
        $page_title = "Receiving";
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/update-order-temp-locations',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('order_item_ids' => $id,'status' => '0'),
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
          ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){
                    return redirect('/pick')->with('message', 'Items picking Rejected!');
                }elseif(($response->success == false)){
                    return back()->with('error', $response->data->error);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }

    }

    public function acceptOrderProcessing($ids)
    {
        $page_title = "Pick";

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/update-order-temp-locations',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('order_item_ids' => $ids,'status' => '1'),
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer '.session('token')
              ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $response = json_decode($response);
            if($response){
                if(isset($response->error) && $response->error == "Unauthenticated."){
                    return redirect('/login');
                }else{
                    if($response->success == true){
                        return redirect('/pick')->with('message', 'Items picking Accepted!');
                    }elseif(($response->success == false)){
                        if($response->message){
                            return back()->with('error',  $response->message);
                        }else{
                            return back()->with('error', 'Something went wrong');
                        }
                    }else{
                        return back()->with('error', 'Something went wrong');
                    }
                }
            }else{
                return redirect('/login');
            }
    }

    public function suggestLocation($barcode)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/putaway-suggest-location/'.$barcode,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer '.session('token')
            ),
        ));

        $response = json_decode(curl_exec($curl));
        curl_close($curl);

        return $response;
    }

    public function assignPickList()
    {
        $page_title = "Assigned Pick List";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "active";

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/assign-picklist/',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer '.session('token')
            ),
        ));

        $response = json_decode(curl_exec($curl));
        curl_close($curl);

        if($response) {

            if(isset($response->error) && $response->error == "Unauthenticated.") {
                return redirect('/login');

            } else {

                if($response->success == true) {
                    $order_list = $response->data;
                } elseif($response->success == false) {
                    $order_list = [];
                    return redirect('/pick')->with('error', $response->message);
                } else {
                    return redirect('/pick')->with('error', 'Something went wrong');
                }
            }
        } else {
            return redirect('/login');
        }

        return redirect('/pick');
    }

    public function releaseAssignedPickList()
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/assign-picklist/release',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer '.session('token')
            ),
        ));

        $response = json_decode(curl_exec($curl));
        curl_close($curl);

        if($response) {

            if(isset($response->error) && $response->error == "Unauthenticated.") {
                return redirect('/login');

            } else {

                if($response->success == true) {
                    return redirect('/pick')->with('success', $response->message);
                } elseif($response->success == false) {
                    return redirect('/pick')->with('error', $response->message);
                }
            }
        }

        return redirect('/pick')->with('error', 'Something went wrong');
    }

    public function submitToPD(Request $request)
    {
        $request->validate([
            'trolley_number' => 'required'
        ]);

        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/assign-picklist/submit-to-pd',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('trolley_number' => $request->trolley_number),
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
          ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true) {
                    return redirect('/pick')->with('success', $response->message);
                } elseif($response->success == false) {
                    return redirect('/pick')->with('error', $response->message);
                }
            }
        }else{
            return redirect('/login');
        }
        curl_close($curl);

        return redirect('/pick')->with('error', 'Something went wrong');
    }

    public function pendingPutAway()
    {
        $page_title = "Pending Put Away";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "active";
        $data['pick_active'] = "";
        $data['show_scanner_on_header'] = true;

    //     $curl = curl_init();
    //     curl_setopt_array($curl, array(
    //     CURLOPT_URL => env('UNITY_URL').'/api/ffc/get-pending-putaway-items',
    //     CURLOPT_RETURNTRANSFER => true,
    //     CURLOPT_ENCODING => '',
    //     CURLOPT_MAXREDIRS => 10,
    //     CURLOPT_TIMEOUT => 0,
    //     CURLOPT_FOLLOWLOCATION => true,
    //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //     CURLOPT_CUSTOMREQUEST => 'GET',
    //     CURLOPT_HTTPHEADER => array(
    //       'Accept: application/json',
    //       'Authorization: Bearer '.session('token')
    //     ),
    //   ));

    //     $response = curl_exec($curl);
    //     $response = json_decode($response);
    //     if($response){
    //         if(isset($response->error) && $response->error == "Unauthenticated."){
    //             return redirect('/login');
    //         }elseif($response->success == false){
    //             if($response->data->error == "No Pending Putaway Found"){
    //                 return redirect('/home')->with('message', 'No pending putaway found against any item!');
    //             }else{
    //                 return redirect('/home')->with('error', $response->data->error);
    //             }
    //         }else{
    //             $pending_putaway_items = $response->data;
    //         }
    //     }else{
    //         return redirect('/login');
    //     }
    //     curl_close($curl);

        $pending_putaway_items = [];
        return view('pending_putaway',compact('page_title','data','pending_putaway_items'));
    }

    public function putAwayNew($id)
    {
        $page_title = "Put Away";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "active";
        $data['pick_active'] = "";
        $data['show_scanner_on_header'] = true;

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/putaway_item',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('product_id' => $id),
            CURLOPT_HTTPHEADER => array(
              'Accept: application/json',
              'Authorization: Bearer '.session('token')
          ),
          ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }elseif($response->success == false){
                if($response->data->error == "Pending Putaway for this Item was not Found"){
                    return redirect('/pending_putaway')->with('message', 'Putaway was completed successfully for this item!');
                }else{
                    return redirect('/pending_putaway')->with('error', $response->data->error);
                }
            }else{
                $pending_putaway_item = $response->data->item_detail;
                $itemWisePutawaySetting = $response->data->ItemwisePutaway;
                $data['locations'] = $response->data;

            }
        }else{
            return redirect('/login');
        }
        curl_close($curl);
        return view('putaway_new',compact('page_title','data','pending_putaway_item', 'itemWisePutawaySetting'));
    }
    public function putAwayLocationFirst(Request $request)
    {
        $page_title = "Put Away Location";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "active";
        $data['pick_active'] = "";
        $data['show_scanner_on_header'] = false;

        return view('putaway_location_first',compact('page_title','data'));
    }

    public function putAwaySaveNew(Request $request){
        $request->validate([
            'product_id' => 'required',
            'location_id' => 'required',
            'qty' => 'required'
        ]);
        $product_id = $request->product_id;
        $location_id = $request->location_id;
        $qty = $request->qty;

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/update-putaway-new',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('product_id' =>  $product_id,'location_id' =>$location_id,'qty' => $qty),
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer '.session('token')
              ),
          ));

        $response = curl_exec($curl);

        curl_close($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){
                    return redirect('/putaway_new/'.$product_id)->with('message', 'Put away submitted');
                }elseif(($response->success == false)){
                    return back()->with('error', $response->data->error);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }

    }


    public function transferOutbound()
    {
        $page_title = "Transfer Orders";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "active";


        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/transfer-order/get-unassigned',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
          ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){
                    $order_list = $response->data->orders;
                    $order_ids = $response->data->all_order_ids;
                }elseif($response->success == false){
                    $order_list = [];
                    $order_ids = [];
                    if(isset($response->data->error) && $response->data->error == "Permission Denied"){
                        return redirect('/home')->with('error', $response->data->error);
                    }
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }
        curl_close($curl);

        return view('transfer-outbound',compact('page_title','order_list', 'order_ids', 'data'));
    }


    public function transferOutboundPickItems($id, $automated_selection = 0)
    {
        $page_title = "Picking";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "active";
        $data['picked'] = false;
        $data['show_scanner_on_header'] = true;

        $so_id = $id;
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => env('UNITY_URL').'/api/ffc/transfer-order/get-details-for-picking',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => array('order_id' => $so_id, 'automated_selection' => $automated_selection),
          CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);

        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){

                    if ($response->message == 'picked') {
                        $data['picked'] = true;
                    }
                    
                    $order_items = $response->data->data;
                    $order_data = $response->data->order_data;
                    $page_title = '#'.$order_data->reference_id;
                    $item_wise_transfer_order_setting = $response->data->item_wise_scan_at_transfer_order;

                }elseif($response->success == false){
                    $order_items = [];
                    $order_data = [];
                    $order_id = 0;
                    return back()->with('error', $response->message);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }

        return view('transfer-outbound-pickitems',compact('page_title','order_items','order_data','data','item_wise_transfer_order_setting','automated_selection'));
    }


    public function transferOutboundPickItemFfcLocations($id)
    {
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "active";
        $data['show_scanner_on_header'] = true;
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => env('UNITY_URL').'/api/ffc/transfer-order/get-details-for-picking-item',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => array('item_id' => $id),
          CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);

        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){

                    $ffc_inventories = $response->data->data;
                    $item_data = $response->data->item_data;
                    $page_title = '#'.$item_data->sku.' ('.$item_data->barcode.')';
                    return view('transfer-outbound-pickitem-assign-location',compact('page_title','ffc_inventories','data','item_data'));

                }elseif($response->success == false){
                    return back()->with('error', $response->message);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }
    }


    public function transferOutboundPickItemFfcLocationsCompleted(Request $request)
    {
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "";
        $data['pick_active'] = "active";
        $data['show_scanner_on_header'] = true;
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => env('UNITY_URL').'/api/ffc/transfer-order/assign-ffc-location-to-picking-item',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => array('data' => $request->picked_item),
          CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);

        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true){

                    return redirect('/transfer-outbound/pickitems/'.$response->data->stock_transfer_fulfillment_order_id)->with('message', $response->message);

                }elseif($response->success == false){
                    return back()->with('error', $response->message);
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }
    }


    // public function acceptTransferOrderProcessing($id)
    // {
    //     $page_title = "Pick";

    //     $curl = curl_init();

    //     curl_setopt_array($curl, array(
    //         CURLOPT_URL => env('UNITY_URL').'/api/ffc/transfer-order/update-order-temp-locations',
    //         CURLOPT_RETURNTRANSFER => true,
    //         CURLOPT_ENCODING => '',
    //         CURLOPT_MAXREDIRS => 10,
    //         CURLOPT_TIMEOUT => 0,
    //         CURLOPT_FOLLOWLOCATION => true,
    //         CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //         CURLOPT_CUSTOMREQUEST => 'POST',
    //         CURLOPT_POSTFIELDS => array('id' => $id,'status' => '1'),
    //         CURLOPT_HTTPHEADER => array(
    //             'Accept: application/json',
    //             'Authorization: Bearer '.session('token')
    //           ),
    //         ));

    //         $response = curl_exec($curl);

    //         curl_close($curl);
    //         $response = json_decode($response);

    //         if($response){
    //             if(isset($response->error) && $response->error == "Unauthenticated."){
    //                 return redirect('/login');
    //             }else{
    //                 if($response->success == true){
    //                     return redirect('/transfer-outbound')->with('message', 'Items picking Accepted!');
    //                 }elseif(($response->success == false)){
    //                     if($response->message){
    //                         return back()->with('error',  $response->message);
    //                     }else{
    //                         return back()->with('error', 'Something went wrong');
    //                     }
    //                 }else{
    //                     return back()->with('error', 'Something went wrong');
    //                 }
    //             }
    //         }else{
    //             return redirect('/login');
    //         }
    // }



    public function transferOutboundSubmitToPD(Request $request)
    {
        $request->validate([
            'trolley_number' => 'required'
        ]);

        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/transfer-order/submit-to-pd',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('trolley_number' => $request->trolley_number,'tso_id' => $request->tso_id),
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
          ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);

        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true) {
                    return redirect('/transfer-outbound')->with('success', $response->message);
                } elseif($response->success == false) {
                    return redirect('/transfer-outbound')->with('error', $response->message);
                }
            }
        }else{
            return redirect('/login');
        }
        curl_close($curl);

        return redirect('/transfer-outbound')->with('error', 'Something went wrong');
    }



    public function transferOutboundItemPicked(Request $request)
    {
        $request->validate([
            'quantity' => 'required',
            'pick_item' => 'required',
            'o_id' => 'required',
        ]);

        $automated_selection = 0;
        if(isset($request->automated_selection)){
            $automated_selection = $request->automated_selection;
        }

        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/transfer-order/item-picked',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('pick_item' => $request->pick_item,'quantity' => $request->quantity,'o_id' => $request->o_id,'seller_location_id' => $request->seller_location_id),
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer '.session('token')
          ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                if($response->success == true) {
                    return redirect('/transfer-outbound/pickitems/'.$request->o_id.'/'.$automated_selection)->with('success', $response->message);
                } elseif($response->success == false) {
                    return redirect('/transfer-outbound/pickitems/'.$request->o_id.'/'.$automated_selection)->with('error', $response->message);
                }
            }
        }else{
            return redirect('/login');
        }

        return redirect('/transfer-outbound')->with('error', 'Something went wrong');
    }

    public function stockPicking()
    {
        $page_title = "Stock Movement";
        $data['home_active'] = "";
        $data['security_active'] = "";
        $data['putaway_active'] = "active";
        $data['pick_active'] = "";
        $data['show_scanner_on_header'] = true;

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('UNITY_URL').'/api/ffc/check-for-stock-movement/',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer '.session('token')
            ),
        ));
        $response = curl_exec($curl);
        $response = json_decode($response);
        curl_close($curl);
        if($response){
            if(isset($response->error) && $response->error == "Unauthenticated."){
                return redirect('/login');
            }else{
                $products_for_putaway = [];
                if($response->success == true){
                    $picking_data = $response->data;
                    $item_wise_stock_movement_setting = $response->data->item_wise_scan_at_stock_movement;

                    if ($picking_data->products) {
                        foreach (collect($picking_data->products)->groupBy('product.barcode') as $key => $value) {
                            $products_for_putaway[] = [
                                'product' => $value[0]->product,
                                'qty_picked' => $value->sum('qty_picked'),
                                'qty_putaway' => $value->sum('qty_putaway'),
                            ];
                        }
                    }

                    return view('stock_movement_main',compact('page_title','picking_data','data','item_wise_stock_movement_setting','products_for_putaway'));
                }elseif($response->success == false){
                    $picking_data = [];
                    if(isset($response->data->error) && $response->data->error == "Permission Denied"){
                        return redirect('/home')->with('error', $response->data->error);
                    }else{
                        $item_wise_stock_movement_setting = $response->data->item_wise_scan_at_stock_movement;
                        return view('stock_movement_main',compact('page_title','picking_data','data','item_wise_stock_movement_setting','products_for_putaway'));
                    }
                }else{
                    return back()->with('error', 'Something went wrong');
                }
            }
        }else{
            return redirect('/login');
        }
    }





    public function itemCountingModeSwitch(Request $request, $item_counting_mode)
    {
        $error = 1;
        $data['item_counting_mode'] = $item_counting_mode;
        $data = json_encode($data);

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('UNITY_URL').'/api/ffc/setting/item-counting-mode',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.session('token')
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response);
        
        if($response) {
            
            if(isset($response->error) && $response->error == "Unauthenticated.") {
                $message = 'Failed | Please reload the page and try again';

            } else {

                if($response->success == true) {
                    $error = 0;
                    $message = 'Success | '.$response->message;
                    session(['ffc_app_item_counting_mode_value' => $item_counting_mode]);


                } elseif($response->success == false) {
                    $message = 'Failed | '.$response->data->error;
                } else{
                    $message = 'Failed | Something went wrong';
                }
            }

        } else {
            $message = 'Failed | Please reload the page and try again';
        }
        curl_close($curl);

        return compact('error', 'message');
    }



}
