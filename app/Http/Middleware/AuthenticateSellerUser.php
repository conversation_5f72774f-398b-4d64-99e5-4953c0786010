<?php

namespace App\Http\Middleware;

use Closure;

class AuthenticateSellerUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if( session('token') && session('token') != "")
        {
            return $next($request);
        }else{
            return redirect('/login');
        }
    }
}
