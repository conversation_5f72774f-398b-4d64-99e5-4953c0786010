version: '3.8'


services:
  nginxcoreffc:
    container_name: nginxcoreffc

    image: nginx:stable-alpine
    ports:
      - "8083:80"
    depends_on:
      - php81ffc
    volumes:
      - .:/var/www
      - ./nginx/conf.d/:/etc/nginx/conf.d

  php81ffc:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: php81ffc
    volumes:
      - .:/var/www
    ports:
      - "9011:9000"
