.offcanvas.action-sheet {
    border: 0;
    height: auto;
    z-index: 9999;
    .offcanvas-body {
        padding: 0;
        .action-sheet-content{
            padding: 20px 16px;
            max-height: 460px;
            overflow: auto;
        }
    }
    .offcanvas-header {
        padding: 2px 20px;
        display: block;
        min-height: auto;
        box-shadow: none;
        border-bottom: 1px solid $colorLine;
        .offcanvas-title {
            padding: 0;
            margin: 0;
            text-align: center;
            display: block;
            font-size: $fontSizeSub;
            padding: 6px 0;
            color: $colorHeading;
            font-weight: $medium;
        }
        .close-button{
            font-size: 20px;
            color: $colorLight;
            position: absolute;
            right: 0;
            top: 2px;
            width: 48px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            &:hover,
            &:active{
                opacity: .7;
            }
        }
    }
}
.offcanvas.action-sheet.inset {
    left: 16px;
    right: 16px;
    width: auto;
    bottom: 16px;
    border-radius: 16px;
    padding-bottom: 0;
    margin-bottom: $safeBottom;
    .action-button-list {
        > li:last-child {
            .btn {
                border-radius: 0 0 16px 16px !important;
            }
        }
    }
}

.action-button-list {
    padding: 0;
    margin: 0;
    > li {
        display: block;
        .btn {
            padding: 0;
            display: flex;
            width: 100%;
            min-height: 50px;
            font-size: $fontSize;
            color: $colorHeading;
            line-height: 1.1em;
            justify-content: space-between;
            font-weight: $regular;
            border-radius: 0 !important;
            padding: 10px 16px;
            &:active,
            &.active {
                background: rgba($colorLine, 0.3);
            }
            &.text-primary {
                color: $colorPrimary !important;
            }
            &.text-secondary {
                color: $colorSecondary !important;
            }
            &.text-danger {
                color: $colorDanger !important;
            }
            &.text-success {
                color: $colorSuccess !important;
            }
            &.text-warning {
                color: $colorWarning !important;
            }
            &.text-info {
                color: $colorInfo !important;
            }
            &.text-light {
                color: #fff !important;
            }
            &.text-dark {
                color: $colorHeading !important;
            }
            > span {
                display: flex;
                align-items: center;
                justify-content: flex-start;
            }
            i.bi,
            i.icon,
            ion-icon {
                width: 24px !important;
                height: 24px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                margin-right: 10px;
                margin-left: -5px;
                font-size: 24px;
                line-height: 1em;
            }
        }
        &.action-divider {
            height: 1px;
            background: $colorLine;
        }
    }
}