.story-block {
    .splide__slide {
        padding-top: 5px;
        padding-bottom: 5px;
        padding-right: 26px !important;
        margin-right: -10px !important;
        img {
            box-shadow: 0 0 0 3px #ffffff, 0 0 0 5px $colorPrimary;
        }
    }
    .seen {
        img {
            box-shadow: 0 0 0 4px #ffffff, 0 0 0 5px $colorLight;
        }
    }
    .passive {
        img {
            box-shadow: 0 0 0 4px #ffffff, 0 0 0 5px $colorLight;
            filter: grayscale(100%) opacity(50%);
        }
    }
}


.modal.stories {
    padding-top: $safeTop;
    overflow: hidden;
    .modal-dialog {
        transform: translate(0, 100%) scale(0.1) !important;
        min-width: 100%;
        margin: 0;
        transition: 0.2s ease-in-out !important;
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin-top: $safeTop;
        .modal-content {
            border-radius: 0;
            border: 0;
            margin: auto;
            overflow: hidden;
            padding-top: 56px;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            .modal-header {
                border: 0;
                padding: 0;
                min-height: 56px;
                padding: 16px 16px 42px 16px;
                border-radius: 0;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin: 0;
                position: fixed;
                left: 0;
                right: 0;
                top: 0;
                width: 100%;
                z-index: 100;
                background: transparent;
                background-image: linear-gradient(-180deg, rgba(0, 0, 0, 0.85) 0%, rgba(0, 0, 0, 0) 100%);
                .modal-title {
                    margin: 0;
                    color: $colorHeading;
                    font-size: $fontSizeHeading;
                }
            }
            .modal-body {
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                padding: 100px 24px;
                p {
                    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.59);
                    opacity: 0.9;
                }
                h1,
                h2,
                h3,
                h4,
                h5 {
                    color: #fff;
                    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.59);
                }
                .btn {
                    margin-top: 10px;
                }
                .fixed-btn {
                    position: absolute;
                    left: 16px;
                    right: 16px;
                    bottom: 16px;
                    margin-bottom: $safeBottom;
                }
            }
        }
    }
    &.show .modal-dialog {
        transform: translate(0, 0) scale(1) !important;
    }
    &.with-story-bar {
        .modal-content {
            .modal-header {
                padding-top: 32px;
            }
        }
    }
    .story-bar {
        height: 4px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.2);
        position: absolute;
        left: 16px;
        right: 16px;
        z-index: 1000;
        top: 16px;
        overflow: hidden;
        span {
            display: block;
            height: 4px;
            width: 0%;
            background: #fff;
        }
    }
    @keyframes storybar-animation {
        0% {
            width: 0;
        }
        100% {
            width: 100%;
        }
    }
    .story-image {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        background: #000;
        img,
        video {
            width: auto;
            height: 100vh;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: 0;
        }
    }
    .profile-detail {
        display: flex;
        align-items: center;
        h4 {
            color: #fff;
            margin: 0;
            line-height: 1.2em;
            display: block;
        }
        .text-muted {
            display: block;
            font-size: $fontSizeCaption;
            color: #fff !important;
            opacity: 0.7;
            line-height: 1.3em;
        }
    }
    .close-stories {
        color: #fff;
        font-size: 24px;
        &:hover{
            opacity: .6;
        }
    }
}
