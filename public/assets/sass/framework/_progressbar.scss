.progress {
    height: 14px;
    border-radius: $borderRadius;
}
.progress-bar {
    background: $colorPrimary;
    height: 14px;
    line-height: 1em;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: $medium;
    letter-spacing: 0;
}

.circle-progress2 {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    .inner {
        position: absolute;
        text-align: center;
        font-size: $fontSizeCaption !important;
        line-height: 1.2em;
        h4 {
            margin: 0;
            font-weight: $bold;
            font-size: $fontSizeHeading;
        }
    }
    & > svg {
        height: 100%;
        display: block;
    }
}
.circle-progress-container {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    .inner {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        align-items: center;
        justify-content: center;
        font-size: $fontSizeCaption !important;
        line-height: 1.2em;
        h4 {
            margin: 0;
            font-weight: $bold;
            font-size: $fontSizeHeading;
        }
    }
    svg {
        height: 100% !important;
        width: 100% !important;
        display: block;
    }
}
