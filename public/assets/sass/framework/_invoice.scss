
.invoiceBackgroundLogo{
    position: absolute;
    left: 5%;
    top: 50%;
    transform: translate(0, -50%) rotate(25deg);
    img{
        opacity: .1;
        width: 90%;
    }
}
.invoice{
    background: #FFF;
    box-shadow: $boxShadow;
    padding: 24px 16px;
    margin: 8px;
    position: relative;
    overflow: hidden;
    .invoice-page-header{
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        .invoice-logo{
            img{
                height: 20px;
            }
        }
        .invoice-id{
            font-size: $fontSizeCaption;
            color: $colorText;
        }
    }
    .invoice-person{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        .invoice-to,
        .invoice-from{
            font-size: $fontSizeCaption;
            h4{
                margin-bottom: 8px;
                color: $colorHeading;
                font-size: $fontSizeSub;
            }
            p{
                margin: 0 0 3px 0;
                line-height: 1.4em;
                color: $colorLight;
            }
        }
        .invoice-to{
            padding-right: 12px;
        }
        .invoice-from{
            text-align: right;
            padding-left: 12px;
        }
    }
    .invoice-detail{
        .table{
            border-bottom: 1px solid $colorLine;
            thead,
            tbody{
                font-size: $fontSizeCaption;
                font-weight: $bold;
                td{
                    text-align: center;
                    &:first-child{
                        text-align: left;
                        padding-left: 0;
                    }
                    &:last-child{
                        padding-right: 0;
                        text-align: right;
                    }
                }
            }
            thead{
                color: $colorLight;
            }
            tbody{
                color: $colorHeading;
            }
        }
    }
    .invoice-total{
        font-size: $fontSizeCaption;
        font-weight: $bold;
        .listview{
            padding: 0;
            li{
                min-height: auto;
                padding: 12px 0;
                .hightext{
                    font-size: $fontSizeSub;
                }
                .totaltext{
                    font-size: $fontSizeHeading;
                }
            }
        }
    }
    .invoice-signature{
        .signature-block{
            display: inline-flex;
            font-size: $fontSizeCaption;
            padding: 5px 32px 42px 32px;
            border: 2px dashed $colorLine;
        }
    }
    .invoice-bottom{
        font-size: 10px;
        text-align: center;
        padding-top: 24px;
        opacity: .4;
    }
}