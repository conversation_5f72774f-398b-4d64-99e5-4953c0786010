.btn {
  height: 40px;
  padding: 3px 18px;
  font-size: $fontSizeSub;
  line-height: 1.2em;
  font-weight: $medium;
  box-shadow: none !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: none;
  text-decoration: none !important;
  border-radius: $borderRadius;
  border-width: 2px;
  i.bi,
  i.icon,
  ion-icon {
    font-size: 22px;
    margin-right: 10px;
    margin-top: -2px;
    font-weight: 700;
  }
  &.rounded {
    border-radius: 100px !important;
  }
  &.square {
    border-radius: 0 !important;
  }
  &.shadowed {
    box-shadow: $boxShadow !important;
  }
}
.btn-lg {
  height: 48px;
  padding: 3px 24px;
  font-size: 18px;
  i.bi,
  i.icon,
  ion-icon {
    font-size: 26px;
  }
}
.btn-sm {
  height: 30px;
  padding: 0px 12px;
  font-size: 12px;
  i.bi,
  i.icon,
  ion-icon {
    font-size: 20px;
    margin-right: 6px;
  }
}
.btn-block {
  width: 100%;
}
.btn-group {
  .btn {
    &:active {
      transform: none;
    }
  }
}
.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  i.bi,
  i.icon,
  ion-icon {
    text-align: center;
    margin: 0 !important;
  }
  &.btn-lg {
    width: 48px;
    height: 48px;
    i.bi,
    i.icon,
    ion-icon {
      font-size: 32px;
    }
  }
  &.btn-sm {
    width: 30px;
    height: 30px;
    i.bi,
    i.icon,
    ion-icon {
      font-size: 18px;
    }
  }
}

@mixin buttonStyle($color) {
  background: $color !important;
  border-color: $color !important;
  color: #ffffff !important;
  &:hover,
  &:focus,
  &:active,
  &.active {
    background: darken($color, 5%) !important;
    border-color: darken($color, 5%) !important;
  }
  &.disabled,
  &:disabled {
    background: $color;
    border-color: $color;
    opacity: 0.5;
  }
}
.btn-primary {
  @include buttonStyle($colorPrimary);
}
.btn-secondary {
  @include buttonStyle($colorSecondary);
}
.btn-success {
  @include buttonStyle($colorSuccess);
}
.btn-danger {
  @include buttonStyle($colorDanger);
}
.btn-warning {
  color: #fff !important;
  @include buttonStyle($colorWarning);
}
.btn-link {
  color: $colorPrimary !important;
}
.btn-info {
  @include buttonStyle($colorInfo);
}
.btn-dark {
  @include buttonStyle(#333);
}
.btn-light {
  @include buttonStyle(rgba(255, 255, 255, 0.5));
  color: $colorHeading !important;
}
@mixin buttonOutlineStyle($color) {
  background: transparent;
  border-color: $color;
  border-width: 1px !important;
  color: $color;
  transition: none;
  &:hover,
  &:active {
    background: rgba($color, 0.15) !important;
    border-color: $color !important;
    color: $color !important;
  }
  &.active {
    background: $color !important;
    color: #fff !important;
    border-color: $color !important;
  }
  &.disabled,
  &:disabled {
    color: $color !important;
    border-color: $color !important;
    background: transparent !important;
    opacity: 0.5;
  }
}
.btn-outline-primary {
  @include buttonOutlineStyle($colorPrimary);
}
.btn-outline-secondary {
  @include buttonOutlineStyle($colorSecondary);
}
.btn-outline-success {
  @include buttonOutlineStyle($colorSuccess);
}
.btn-outline-danger {
  @include buttonOutlineStyle($colorDanger);
}
.btn-outline-warning {
  @include buttonOutlineStyle($colorWarning);
}
.btn-outline-info {
  @include buttonOutlineStyle($colorInfo);
}
.btn-outline-light {
  @include buttonOutlineStyle(#fff);
  &:active {
    color: $colorHeading !important;
  }
}
.btn-outline-dark {
  border-width: 1px;
}
@mixin buttonTextStyle($color) {
  background: transparent;
  border-color: transparent;
  color: $color !important;
  transition: none;
  &:hover {
    background: transparent;
  }
  &:active,
  &.active {
    background: rgba($color, 0.15) !important;
    border-color: transparent !important;
    color: $color !important;
  }
  &.disabled,
  &:disabled {
    color: $color !important;
    border-color: $color !important;
    background: transparent !important;
    opacity: 0.5;
  }
}
.btn-text-primary {
  @include buttonTextStyle($colorPrimary);
}
.btn-text-secondary {
  @include buttonTextStyle($colorSecondary);
}
.btn-text-success {
  @include buttonTextStyle($colorSuccess);
}
.btn-text-danger {
  @include buttonTextStyle($colorDanger);
}
.btn-text-warning {
  @include buttonTextStyle($colorWarning);
}
.btn-text-info {
  @include buttonTextStyle($colorInfo);
}
.btn-text-light {
  background: transparent;
  color: #fff;
  &:hover,
  &:active,
  &.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
  }
}
.btn-text-dark {
  @include buttonTextStyle($colorHeading);
}
.btn-group {
  .btn {
    margin-left: -1px !important;
  }
}

.btn-facebook {
  color: #fff !important;
  background: #39579b;
  &:hover,
  &:active {
    background: darken(#39579b, 5%);
  }
}
.bg-facebook {
  background: #39579b;
  color: #fff !important;
}

.btn-twitter {
  color: #fff !important;
  background: #049ff6;
  &:hover,
  &:active {
    background: darken(#049ff6, 5%);
  }
}
.bg-twitter {
  background: #049ff6;
  color: #fff !important;
}

.btn-instagram {
  color: #fff !important;
  background: #df237b;
  &:hover,
  &:active {
    background: darken(#df237b, 5%);
  }
}
.bg-instagram {
  background: #de0067;
  color: #fff !important;
}

.btn-linkedin {
  color: #fff !important;
  background: #0075aa;
  &:hover,
  &:active {
    background: darken(#0075aa, 5%);
  }
}
.bg-linkedin {
  background: #0075aa;
  color: #fff !important;
}

.btn-twitch {
  color: #fff !important;
  background: #923cff;
  &:hover,
  &:active {
    background: darken(#923cff, 5%);
  }
}
.bg-twitch {
  background: #923cff;
  color: #fff !important;
}

.btn-whatsapp {
  color: #fff !important;
  background: #0ad561;
  &:hover,
  &:active {
    background: darken(#0ad561, 5%);
  }
}
.bg-whatsapp {
  background: #0ad561;
  color: #fff !important;
}

.btn-youtube {
  color: #fff !important;
  background: #ff0000;
  &:hover,
  &:active {
    background: darken(#ff0000, 5%);
  }
}
.bg-youtube {
  background: #ff0000;
  color: #fff !important;
}

.btn-amazon {
  color: #fff !important;
  background: #292929;
  &:hover,
  &:active {
    background: darken(#292929, 5%);
  }
}
.bg-amazon {
  background: #292929;
  color: #fff !important;
}

.btn-android {
  color: #fff !important;
  background: #7dd159;
  &:hover,
  &:active {
    background: darken(#7dd159, 5%);
  }
}
.bg-android {
  background: #7dd159;
  color: #fff !important;
}

.btn-apple {
  color: #fff !important;
  background: #000;
  &:hover,
  &:active {
    background: #222;
  }
}
.bg-apple {
  background: #000;
  color: #fff !important;
}

.btn-dribbble {
  color: #fff !important;
  background: #ec4989;
  &:hover,
  &:active {
    background: darken(#ec4989, 5%);
  }
}
.bg-dribbble {
  background: #ec4989;
  color: #fff !important;
}

.btn-skype {
  color: #fff !important;
  background: #00a8f3;
  &:hover,
  &:active {
    background: darken(#00a8f3, 5%);
  }
}
.bg-skype {
  background: #00a8f3;
  color: #fff !important;
}

.btn-pinterest {
  color: #fff !important;
  background: #f12a2c;
  &:hover,
  &:active {
    background: darken(#f12a2c, 5%);
  }
}
.bg-pinterest {
  background: #f12a2c;
  color: #fff !important;
}

.btn-dropbox {
  color: #fff !important;
  background: #005cff;
  &:hover,
  &:active {
    background: darken(#005cff, 5%);
  }
}
.bg-dropbox {
  background: #005cff;
  color: #fff !important;
}

.btn-bitcoin {
  color: #fff !important;
  background: #f99400;
  &:hover,
  &:active {
    background: darken(#f99400, 5%);
  }
}
.bg-bitcoin {
  background: #f99400;
  color: #fff !important;
}

.btn-github {
  color: #fff !important;
  background: #323131;
  &:hover,
  &:active {
    background: darken(#323131, 5%);
  }
}
.bg-github {
  background: #323131;
  color: #fff !important;
}

@mixin radiobuttons($color) {
  background: $color !important;
  border-color: $color !important;
}
.btn-check:checked + .btn-outline-primary,
.btn-check:checked + .btn-outline-secondary,
.btn-check:checked + .btn-outline-danger,
.btn-check:checked + .btn-outline-warning,
.btn-check:checked + .btn-outline-success,
.btn-check:checked + .btn-outline-info,
.btn-check:checked + .btn-outline-dark{
  color: #fff !important;
  transition: none;
}

.btn-check:checked + .btn-outline-primary {
  @include radiobuttons($colorPrimary);
}
.btn-check:checked + .btn-outline-secondary {
  @include radiobuttons($colorSecondary);
}
.btn-check:checked + .btn-outline-warning {
  @include radiobuttons($colorWarning);
}
.btn-check:checked + .btn-outline-danger {
  @include radiobuttons($colorDanger);
}
.btn-check:checked + .btn-outline-success {
  @include radiobuttons($colorSuccess);
}
.btn-check:checked + .btn-outline-info {
  @include radiobuttons($colorInfo);
}
.btn-check:checked + .btn-outline-dark {
  @include radiobuttons(#222);
}


